#include "smart_broadcast_handler.h"
#include "protocols/protocol.h"
#include <esp_log.h>
#include <cJSON.h>

#define TAG "SmartBroadcastHandler"

SmartBroadcastHandler::SmartBroadcastHandler(Protocol* protocol) 
    : protocol_(protocol), is_playing_(false) {
    ESP_LOGI(TAG, "SmartBroadcastHandler initialized");
}

SmartBroadcastHandler::~SmartBroadcastHandler() {
    StopPlayback();
    ESP_LOGI(TAG, "SmartBroadcastHandler destroyed");
}

void SmartBroadcastHandler::SendQuery(const std::string& query,
                                     QueryType query_type,
                                     std::function<void(bool success)> callback) {
    if (!protocol_) {
        ESP_LOGE(TAG, "Protocol not initialized");
        if (callback) callback(false);
        return;
    }

    if (is_playing_) {
        ESP_LOGW(TAG, "Already playing, stopping current playback");
        StopPlayback();
    }

    ESP_LOGI(TAG, "Sending smart query (type=%d): %s", query_type, query.c_str());

    current_query_ = query;
    current_completion_callback_ = callback;
    is_playing_ = true;

    // 触发播放开始回调
    if (on_playback_started_) {
        on_playback_started_(query);
    }

    // 首先建立音频通道，然后发送STT消息
    if (!protocol_->IsAudioChannelOpened()) {
        ESP_LOGI(TAG, "Opening audio channel for smart query");
        if (!protocol_->OpenAudioChannel()) {
            ESP_LOGE(TAG, "Failed to open audio channel for smart query");
            OnSmartResponse(false);
            return;
        }
    }

    // 发送STT消息
    SendSTTMessage(query, query_type);
}

void SmartBroadcastHandler::QueryWeather() {
    SendQuery("今天天气怎么样", WEATHER_QUERY);
}

void SmartBroadcastHandler::QueryNews() {
    SendQuery("播报今日新闻", NEWS_BRIEF);
}

void SmartBroadcastHandler::SendCustomQuery(const std::string& query) {
    SendQuery(query, CUSTOM_QUERY);
}

void SmartBroadcastHandler::StopPlayback() {
    if (is_playing_) {
        ESP_LOGI(TAG, "Stopping playback");
        is_playing_ = false;
        current_query_.clear();
        
        // 触发完成回调
        if (current_completion_callback_) {
            current_completion_callback_(false);
            current_completion_callback_ = nullptr;
        }
    }
}

void SmartBroadcastHandler::OnPlaybackStarted(std::function<void(const std::string& query)> callback) {
    on_playback_started_ = callback;
}

void SmartBroadcastHandler::OnPlaybackCompleted(std::function<void(const std::string& query, bool success)> callback) {
    on_playback_completed_ = callback;
}

void SmartBroadcastHandler::SendSTTMessage(const std::string& query, QueryType query_type) {
    std::string stt_message = BuildSTTMessage(query, query_type);

    protocol_->SendMcpMessage(stt_message);
    ESP_LOGI(TAG, "Smart query sent via STT, waiting for server response...");

    // 不再立即模拟成功响应，等待服务器的真实响应
    // 响应将通过Application的OnIncomingJson回调处理
}

void SmartBroadcastHandler::OnSmartResponse(bool success) {
    ESP_LOGI(TAG, "Smart response received: %s", success ? "success" : "failed");

    is_playing_ = false;

    // 触发完成回调
    if (on_playback_completed_) {
        on_playback_completed_(current_query_, success);
    }

    if (current_completion_callback_) {
        current_completion_callback_(success);
        current_completion_callback_ = nullptr;
    }

    current_query_.clear();
}

void SmartBroadcastHandler::HandleTTSResponse(const std::string& state, const std::string& text) {
    ESP_LOGI(TAG, "Handling TTS response: state=%s, text_length=%d", state.c_str(), text.length());

    if (state == "start") {
        ESP_LOGI(TAG, "TTS playback started");
        // TTS开始播放，保持is_playing_状态
    } else if (state == "stop") {
        ESP_LOGI(TAG, "TTS playback completed");
        // TTS播放完成，调用成功回调
        OnSmartResponse(true);
    } else if (state == "sentence_start") {
        if (!text.empty()) {
            ESP_LOGI(TAG, "TTS sentence: %s", text.c_str());
        }
    } else {
        ESP_LOGW(TAG, "Unknown TTS state: %s", state.c_str());
    }
}

std::string SmartBroadcastHandler::BuildSTTMessage(const std::string& query, QueryType query_type) const {
    cJSON* root = cJSON_CreateObject();
    cJSON* jsonrpc = cJSON_CreateString("2.0");
    cJSON* method = cJSON_CreateString("stt");
    cJSON* id = cJSON_CreateNumber(1);
    
    cJSON* params = cJSON_CreateObject();
    cJSON* text = cJSON_CreateString(query.c_str());
    cJSON* context = cJSON_CreateString(GetContextForQueryType(query_type).c_str());
    
    cJSON_AddItemToObject(params, "text", text);
    cJSON_AddItemToObject(params, "context", context);
    
    cJSON_AddItemToObject(root, "jsonrpc", jsonrpc);
    cJSON_AddItemToObject(root, "method", method);
    cJSON_AddItemToObject(root, "params", params);
    cJSON_AddItemToObject(root, "id", id);
    
    char* json_string = cJSON_Print(root);
    std::string result(json_string);
    
    free(json_string);
    cJSON_Delete(root);
    
    return result;
}

std::string SmartBroadcastHandler::GetContextForQueryType(QueryType query_type) const {
    switch (query_type) {
        case WEATHER_QUERY:
            return "天气查询，请提供详细的天气信息";
        case NEWS_BRIEF:
            return "新闻播报，请提供简洁的新闻摘要";
        case GENERAL_QUERY:
            return "通用查询，请提供准确的信息";
        case CUSTOM_QUERY:
        default:
            return "智能助手查询";
    }
}
