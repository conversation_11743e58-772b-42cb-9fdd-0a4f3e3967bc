#include "local_audio_manager.h"
#include "board.h"
#include <esp_log.h>
#include <esp_vfs.h>
#include <esp_spiffs.h>
#include <dirent.h>
#include <sys/stat.h>
#include <string.h>
#include <algorithm>

#define TAG "LocalAudioManager"

// 音频文件存储路径
const char* LocalAudioManager::AUDIO_BASE_PATH = "/audio";

LocalAudioManager& LocalAudioManager::GetInstance() {
    static LocalAudioManager instance;
    return instance;
}

void LocalAudioManager::Initialize() {
    if (initialized_) {
        return;
    }

    ESP_LOGI(TAG, "Initializing LocalAudioManager");

    // 挂载音频分区
    MountAudioPartition();

    // 扫描音频文件
    ScanAudioFiles();

    initialized_ = true;
    ESP_LOGI(TAG, "LocalAudioManager initialized with %d audio files", audio_files_.size());
}

void LocalAudioManager::PlayAudio(const std::string& audio_number, 
                                 std::function<void(bool success)> callback) {
    if (!initialized_) {
        ESP_LOGE(TAG, "LocalAudioManager not initialized");
        if (callback) callback(false);
        return;
    }
    
    if (!IsValidAudioNumber(audio_number)) {
        ESP_LOGE(TAG, "Invalid audio number format: %s", audio_number.c_str());
        if (callback) callback(false);
        return;
    }
    
    if (is_playing_) {
        ESP_LOGW(TAG, "Already playing, stopping current playback");
        StopPlayback();
    }
    
    auto it = audio_files_.find(audio_number);
    if (it == audio_files_.end()) {
        ESP_LOGE(TAG, "Audio file not found: %s", audio_number.c_str());
        if (callback) callback(false);
        return;
    }
    
    ESP_LOGI(TAG, "Playing local audio: %s -> %s", audio_number.c_str(), it->second.c_str());
    
    current_audio_ = audio_number;
    current_completion_callback_ = callback;
    is_playing_ = true;
    
    // 触发播放开始回调
    if (on_playback_started_) {
        on_playback_started_(audio_number);
    }
    
    // 播放音频文件
    PlayAudioFile(it->second);
}

bool LocalAudioManager::AudioExists(const std::string& audio_number) {
    return audio_files_.find(audio_number) != audio_files_.end();
}

std::vector<std::string> LocalAudioManager::GetAvailableAudios() {
    std::vector<std::string> result;
    for (const auto& pair : audio_files_) {
        result.push_back(pair.first);
    }
    std::sort(result.begin(), result.end());
    return result;
}

void LocalAudioManager::StopPlayback() {
    if (is_playing_) {
        ESP_LOGI(TAG, "Stopping audio playback");
        is_playing_ = false;
        
        // TODO: 实际停止音频播放
        
        // 触发完成回调
        OnAudioPlaybackComplete(false);
    }
}

void LocalAudioManager::OnPlaybackStarted(std::function<void(const std::string& audio_number)> callback) {
    on_playback_started_ = callback;
}

void LocalAudioManager::OnPlaybackCompleted(std::function<void(const std::string& audio_number, bool success)> callback) {
    on_playback_completed_ = callback;
}

std::string LocalAudioManager::GetAudioPath(const std::string& audio_number) {
    auto it = audio_files_.find(audio_number);
    if (it != audio_files_.end()) {
        return it->second;
    }
    return "";
}

void LocalAudioManager::RefreshAudioList() {
    ESP_LOGI(TAG, "Refreshing audio file list");
    audio_files_.clear();
    ScanAudioFiles();
    ESP_LOGI(TAG, "Audio list refreshed, found %d files", audio_files_.size());
}

void LocalAudioManager::ScanAudioFiles() {
    ESP_LOGI(TAG, "Scanning audio files in %s", AUDIO_BASE_PATH);
    
    DIR* dir = opendir(AUDIO_BASE_PATH);
    if (dir == nullptr) {
        ESP_LOGW(TAG, "Cannot open audio directory: %s", AUDIO_BASE_PATH);
        return;
    }
    
    struct dirent* entry;
    while ((entry = readdir(dir)) != nullptr) {
        if (entry->d_type == DT_REG) { // 只处理普通文件
            std::string filename = entry->d_name;
            
            // 检查文件扩展名
            if (filename.length() > 4) {
                std::string ext = filename.substr(filename.length() - 4);
                std::transform(ext.begin(), ext.end(), ext.begin(), ::tolower);
                
                if (ext == ".mp3" || ext == ".wav" || ext == ".p3") {
                    // 提取音频编号（假设格式为 001.mp3, 002.wav等）
                    std::string number = filename.substr(0, filename.length() - 4);
                    if (IsValidAudioNumber(number)) {
                        std::string full_path = std::string(AUDIO_BASE_PATH) + "/" + filename;
                        audio_files_[number] = full_path;
                        ESP_LOGI(TAG, "Found audio file: %s -> %s", number.c_str(), full_path.c_str());
                    }
                }
            }
        }
    }
    
    closedir(dir);
}

void LocalAudioManager::PlayAudioFile(const std::string& file_path) {
    ESP_LOGI(TAG, "Playing audio file: %s", file_path.c_str());
    
    // TODO: 实现实际的音频播放逻辑
    // 这里需要调用Board的音频播放接口
    auto& board = Board::GetInstance();
    auto codec = board.GetAudioCodec();
    
    // 暂时模拟播放成功
    // 在实际实现中，这里应该异步播放音频文件
    // 播放完成后调用OnAudioPlaybackComplete(true)
    
    // 模拟异步播放完成
    OnAudioPlaybackComplete(true);
}

void LocalAudioManager::OnAudioPlaybackComplete(bool success) {
    ESP_LOGI(TAG, "Audio playback completed: %s", success ? "success" : "failed");
    
    is_playing_ = false;
    
    // 触发完成回调
    if (on_playback_completed_) {
        on_playback_completed_(current_audio_, success);
    }
    
    if (current_completion_callback_) {
        current_completion_callback_(success);
        current_completion_callback_ = nullptr;
    }
    
    current_audio_.clear();
}

bool LocalAudioManager::IsValidAudioNumber(const std::string& audio_number) {
    // 检查是否为3位数字格式（001, 002, 等）
    if (audio_number.length() != 3) {
        return false;
    }
    
    for (char c : audio_number) {
        if (!std::isdigit(c)) {
            return false;
        }
    }
    
    return true;
}

void LocalAudioManager::MountAudioPartition() {
    ESP_LOGI(TAG, "Mounting audio partition");

    esp_vfs_spiffs_conf_t conf = {
        .base_path = "/audio",
        .partition_label = "audio",
        .max_files = 20,
        .format_if_mount_failed = true
    };

    esp_err_t ret = esp_vfs_spiffs_register(&conf);
    if (ret != ESP_OK) {
        if (ret == ESP_FAIL) {
            ESP_LOGE(TAG, "Failed to mount or format filesystem");
        } else if (ret == ESP_ERR_NOT_FOUND) {
            ESP_LOGE(TAG, "Failed to find SPIFFS partition");
        } else {
            ESP_LOGE(TAG, "Failed to initialize SPIFFS (%s)", esp_err_to_name(ret));
        }
        return;
    }

    size_t total = 0, used = 0;
    ret = esp_spiffs_info("audio", &total, &used);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to get SPIFFS partition information (%s)", esp_err_to_name(ret));
    } else {
        ESP_LOGI(TAG, "Audio partition size: total: %d, used: %d", total, used);
    }
}
