#ifndef LOCAL_AUDIO_MANAGER_H
#define LOCAL_AUDIO_MANAGER_H

#include <string>
#include <map>
#include <vector>
#include <functional>

/**
 * 本地音频文件管理器
 * 负责管理存储在Flash中的音频文件
 * 支持按编号播放音频文件（001.mp3, 002.mp3等）
 */
class LocalAudioManager {
public:
    static LocalAudioManager& GetInstance();
    
    /**
     * 初始化本地音频管理器
     */
    void Initialize();
    
    /**
     * 播放本地音频文件
     * @param audio_number 音频编号（如"001", "002"）
     * @param callback 播放完成回调
     */
    void PlayAudio(const std::string& audio_number, 
                   std::function<void(bool success)> callback = nullptr);
    
    /**
     * 检查音频文件是否存在
     * @param audio_number 音频编号
     * @return 文件是否存在
     */
    bool AudioExists(const std::string& audio_number);
    
    /**
     * 获取所有可用的音频文件列表
     * @return 音频编号列表
     */
    std::vector<std::string> GetAvailableAudios();
    
    /**
     * 停止当前播放
     */
    void StopPlayback();
    
    /**
     * 获取播放状态
     */
    bool IsPlaying() const { return is_playing_; }
    
    /**
     * 设置回调函数
     */
    void OnPlaybackStarted(std::function<void(const std::string& audio_number)> callback);
    void OnPlaybackCompleted(std::function<void(const std::string& audio_number, bool success)> callback);
    
    /**
     * 获取音频文件路径
     * @param audio_number 音频编号
     * @return 完整的文件路径
     */
    std::string GetAudioPath(const std::string& audio_number);
    
    /**
     * 扫描并更新音频文件列表
     */
    void RefreshAudioList();

private:
    LocalAudioManager() = default;
    ~LocalAudioManager() = default;
    
    // 禁止拷贝和赋值
    LocalAudioManager(const LocalAudioManager&) = delete;
    LocalAudioManager& operator=(const LocalAudioManager&) = delete;
    
    bool initialized_ = false;
    bool is_playing_ = false;
    std::string current_audio_;
    
    // 音频文件映射 (编号 -> 文件路径)
    std::map<std::string, std::string> audio_files_;
    
    // 回调函数
    std::function<void(const std::string&)> on_playback_started_;
    std::function<void(const std::string&, bool)> on_playback_completed_;
    std::function<void(bool)> current_completion_callback_;
    
    // 音频文件基础路径
    static const char* AUDIO_BASE_PATH;
    
    // 内部方法
    void MountAudioPartition();
    void ScanAudioFiles();
    void PlayAudioFile(const std::string& file_path);
    void OnAudioPlaybackComplete(bool success);
    bool IsValidAudioNumber(const std::string& audio_number);
};

#endif // LOCAL_AUDIO_MANAGER_H
