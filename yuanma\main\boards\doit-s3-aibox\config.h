#ifndef _BOARD_CONFIG_H_
#define _BOARD_CONFIG_H_

#include <driver/gpio.h>

#define AUDIO_INPUT_SAMPLE_RATE  16000
#define AUDIO_OUTPUT_SAMPLE_RATE 24000

#define AUDIO_I2S_MIC_GPIO_WS   GPIO_NUM_41
#define AUDIO_I2S_MIC_GPIO_SCK  GPIO_NUM_40
#define AUDIO_I2S_MIC_GPIO_DIN  GPIO_NUM_42
#define AUDIO_I2S_SPK_GPIO_DOUT GPIO_NUM_18
#define AUDIO_I2S_SPK_GPIO_BCLK GPIO_NUM_17
#define AUDIO_I2S_SPK_GPIO_LRCK GPIO_NUM_16

/*
IO9: BUTTON2
IO10: BUTTON3 引出：KEY3
IO15: BUTTON1
*/
#define BUILTIN_LED_GPIO        GPIO_NUM_45
#define BOOT_BUTTON_GPIO        GPIO_NUM_10
#define TOUCH_BUTTON_GPIO       GPIO_NUM_NC
#define VOLUME_UP_BUTTON_GPIO   GPIO_NUM_15
#define VOLUME_DOWN_BUTTON_GPIO GPIO_NUM_9
#define RESET_NVS_BUTTON_GPIO     GPIO_NUM_10
#define RESET_FACTORY_BUTTON_GPIO GPIO_NUM_NC

#endif // _BOARD_CONFIG_H_
