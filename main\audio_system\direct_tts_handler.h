#ifndef DIRECT_TTS_HANDLER_H
#define DIRECT_TTS_HANDLER_H

#include <string>
#include <functional>
#include <map>
#include <vector>

class Protocol;

/**
 * 直接TTS处理器
 * 负责处理不需要大模型的文本转语音播放
 * 通过MCP协议调用服务器端的direct_tts工具
 */
class DirectTTSHandler {
public:
    enum TTSTemplate {
        DRIVING_SAFETY,     // 行车安全提醒
        TIME_ANNOUNCEMENT,  // 时间播报
        SYSTEM_NOTIFICATION,// 系统通知
        CUSTOM_TEXT         // 自定义文本
    };

    DirectTTSHandler(Protocol* protocol);
    ~DirectTTSHandler();
    
    /**
     * 播放直接TTS文本
     * @param text 要播放的文本
     * @param template_type 文本模板类型
     * @param callback 播放完成回调
     */
    void PlayText(const std::string& text, 
                  TTSTemplate template_type = CUSTOM_TEXT,
                  std::function<void(bool success)> callback = nullptr);
    
    /**
     * 播放预定义模板
     */
    void PlayDrivingSafetyTip();
    void PlayTimeAnnouncement();
    void PlaySystemNotification(const std::string& message);
    
    /**
     * 停止当前播放
     */
    void StopPlayback();
    
    /**
     * 状态查询
     */
    bool IsPlaying() const { return is_playing_; }
    
    /**
     * 回调设置
     */
    void OnPlaybackStarted(std::function<void(const std::string& text)> callback);
    void OnPlaybackCompleted(std::function<void(const std::string& text, bool success)> callback);

    /**
     * 处理服务器TTS响应
     */
    void HandleTTSResponse(const std::string& state, const std::string& text = "");

private:
    Protocol* protocol_;
    bool is_playing_ = false;
    std::string current_text_;
    
    // 回调函数
    std::function<void(const std::string&)> on_playback_started_;
    std::function<void(const std::string&, bool)> on_playback_completed_;
    std::function<void(bool)> current_completion_callback_;
    
    // 文本模板管理
    static const std::map<TTSTemplate, std::vector<std::string>> text_templates_;
    
    // 核心方法
    void SendDirectTTSRequest(const std::string& text);
    void OnTTSResponse(bool success);
    std::string GetRandomTemplate(TTSTemplate template_type) const;
    std::string FormatTimeAnnouncement() const;
    
    // MCP消息构造
    std::string BuildMCPMessage(const std::string& text) const;
};

#endif // DIRECT_TTS_HANDLER_H
