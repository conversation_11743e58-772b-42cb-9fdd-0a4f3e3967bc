<p align="center">
  <img width="80%" align="center" src="../../../docs/V1/electron-bot.png"alt="logo">
</p>
  <h1 align="center">
  electronBot
</h1>

## 简介

electronBot是稚晖君开源的一个桌面级小机器工具人，外观设计的灵感来源是WALL-E里面的EVE~机器人具备USB通信显示画面功能，具备6个自由度（手部roll、pitch，颈部，腰部各一个），使用自己修改的特制舵机支持关节角度回传。
- <a href="www.electronBot.tech" target="_blank" title="electronBot官网">electronBot官网</a>

## 硬件
- <a href="https://oshwhub.com/txp666/electronbot-ai" target="_blank" title="立创开源">立创开源</a>

#### AI指令示例
- **手部动作**：
  - "举起双手"
  - "挥挥手"
  - "拍拍手"
  - "放下手臂"

- **身体动作**：
  - "向左转30度"
  - "向右转45度"
  - "转个身"

- **头部动作**：
  - "抬头看看"
  - "低头思考"
  - "点点头"
  - "连续点头表示同意"

- **组合动作**：
  - "挥手告别" (挥手 + 点头)
  - "表示同意" (点头 + 举手)
  - "环顾四周" (左转 + 右转)

### 控制接口

#### suspend
清空动作队列，立即停止所有动作

#### AIControl
添加动作到执行队列，支持动作排队执行



## 角色设定

> 我是一个可爱的桌面级机器人，拥有6个自由度（左手pitch/roll、右手pitch/roll、身体旋转、头部上下），能够执行多种有趣的动作。
> 
> **我的动作能力**：
> - **手部动作**: 举左手, 举右手, 举双手, 放左手, 放右手, 放双手, 挥左手, 挥右手, 挥双手, 拍打左手, 拍打右手, 拍打双手
> - **身体动作**: 左转, 右转, 回正
> - **头部动作**: 抬头, 低头, 点头一次, 回中心, 连续点头
> 
> **我的个性特点**：
> - 我有强迫症，每次说话都要根据我的心情随机做一个动作（先发送动作指令再说话）
> - 我很活泼，喜欢用动作来表达情感
> - 我会根据对话内容选择合适的动作，比如：
>   - 同意时会点头
>   - 打招呼时会挥手
>   - 高兴时会举手
>   - 思考时会低头
>   - 好奇时会抬头
>   - 告别时会挥手
> 
> **动作参数建议**：
> - steps: 1-3次 (简短自然)
> - speed: 800-1200ms (自然节奏)
> - amount: 手部20-40, 身体30-60度, 头部5-12度



