#include <esp_log.h>
#include <esp_err.h>
#include <string>
#include <cstdlib>
#include <cstring>

#include "display.h"
#include "board.h"
#include "application.h"
// #include "font_awesome_symbols.h"  // 无显示模式不需要
#include "audio_codec.h"
#include "settings.h"
#include "assets/lang_config.h"

// 无显示模式下的字符串常量定义
#define FONT_AWESOME_BATTERY_CHARGING "charging"
#define FONT_AWESOME_BATTERY_EMPTY "empty"
#define FONT_AWESOME_BATTERY_1 "low"
#define FONT_AWESOME_BATTERY_2 "medium"
#define FONT_AWESOME_BATTERY_3 "high"
#define FONT_AWESOME_BATTERY_FULL "full"

#define TAG "Display"

Display::Display() {
    // 无显示模式 - 不创建任何LVGL相关资源
    notification_timer_ = nullptr;

    // 初始化所有LVGL对象指针为nullptr
    display_ = nullptr;
    emotion_label_ = nullptr;
    network_label_ = nullptr;
    status_label_ = nullptr;
    notification_label_ = nullptr;
    mute_label_ = nullptr;
    battery_label_ = nullptr;
    chat_message_label_ = nullptr;
    low_battery_popup_ = nullptr;
    low_battery_label_ = nullptr;

    // Create a power management lock
    auto ret = esp_pm_lock_create(ESP_PM_APB_FREQ_MAX, 0, "display_update", &pm_lock_);
    if (ret == ESP_ERR_NOT_SUPPORTED) {
        ESP_LOGI(TAG, "Power management not supported");
    } else {
        ESP_ERROR_CHECK(ret);
    }
}

Display::~Display() {
    // 无显示模式 - 不需要清理LVGL对象
    if (notification_timer_ != nullptr) {
        esp_timer_stop(notification_timer_);
        esp_timer_delete(notification_timer_);
    }

    if (pm_lock_ != nullptr) {
        esp_pm_lock_delete(pm_lock_);
    }
}

void Display::SetStatus(const char* status) {
    // 无显示模式 - no-op
    ESP_LOGD(TAG, "SetStatus: %s", status ? status : "null");
}

void Display::ShowNotification(const std::string &notification, int duration_ms) {
    ShowNotification(notification.c_str(), duration_ms);
}

void Display::ShowNotification(const char* notification, int duration_ms) {
    // 无显示模式 - no-op
    ESP_LOGD(TAG, "ShowNotification: %s (duration: %d ms)", notification ? notification : "null", duration_ms);
}

void Display::UpdateStatusBar(bool update_all) {
    // 无显示模式 - 只更新内部状态，不更新UI
    auto& board = Board::GetInstance();
    auto codec = board.GetAudioCodec();

    // 更新静音状态（不更新UI）
    if (codec->output_volume() == 0 && !muted_) {
        muted_ = true;
    } else if (codec->output_volume() > 0 && muted_) {
        muted_ = false;
    }

    if (pm_lock_ != nullptr) {
        esp_pm_lock_acquire(pm_lock_);
    }

    // 无显示模式 - 只更新电池状态用于低电量提醒，不更新UI
    int battery_level;
    bool charging, discharging;
    const char* icon = nullptr;
    if (board.GetBatteryLevel(battery_level, charging, discharging)) {
        if (charging) {
            icon = FONT_AWESOME_BATTERY_CHARGING;
        } else {
            const char* levels[] = {
                FONT_AWESOME_BATTERY_EMPTY, // 0-19%
                FONT_AWESOME_BATTERY_1,    // 20-39%
                FONT_AWESOME_BATTERY_2,    // 40-59%
                FONT_AWESOME_BATTERY_3,    // 60-79%
                FONT_AWESOME_BATTERY_FULL, // 80-99%
                FONT_AWESOME_BATTERY_FULL, // 100%
            };
            icon = levels[battery_level / 20];
        }

        // 更新内部状态
        battery_icon_ = icon;

        // 低电量提醒（无显示模式下仍需要声音提醒）
        if (strcmp(icon, FONT_AWESOME_BATTERY_EMPTY) == 0 && discharging) {
            static bool low_battery_warned = false;
            if (!low_battery_warned) {
                low_battery_warned = true;
                auto& app = Application::GetInstance();
                app.PlaySound(Lang::Sounds::P3_LOW_BATTERY);
            }
        } else {
            static bool low_battery_warned = false;
            low_battery_warned = false;
        }
    }

    // 无显示模式 - 只更新网络状态，不更新UI
    static int seconds_counter = 0;
    if (update_all || seconds_counter++ % 10 == 0) {
        // 升级固件时，不读取 4G 网络状态，避免占用 UART 资源
        auto device_state = Application::GetInstance().GetDeviceState();
        static const std::vector<DeviceState> allowed_states = {
            kDeviceStateIdle,
            kDeviceStateStarting,
            kDeviceStateWifiConfiguring,
            kDeviceStateListening,
            kDeviceStateActivating,
        };
        if (std::find(allowed_states.begin(), allowed_states.end(), device_state) != allowed_states.end()) {
            icon = board.GetNetworkStateIcon();
            if (icon != nullptr) {
                network_icon_ = icon;
            }
        }
    }

    if (pm_lock_ != nullptr) {
        esp_pm_lock_release(pm_lock_);
    }
}


void Display::SetEmotion(const char* emotion) {
    // 无显示模式 - no-op
    ESP_LOGD(TAG, "SetEmotion: %s", emotion ? emotion : "null");
}

void Display::SetIcon(const char* icon) {
    // 无显示模式 - no-op
    ESP_LOGD(TAG, "SetIcon: %s", icon ? icon : "null");
}

#if HAS_LVGL
void Display::SetPreviewImage(const lv_img_dsc_t* image) {
    // 无显示模式 - no-op
    ESP_LOGD(TAG, "SetPreviewImage called");
}
#else
void Display::SetPreviewImage(const void* image) {
    // 无显示模式 - no-op
    ESP_LOGD(TAG, "SetPreviewImage called (no-display mode)");
}
#endif

void Display::SetChatMessage(const char* role, const char* content) {
    // 无显示模式 - no-op
    ESP_LOGD(TAG, "SetChatMessage: %s: %s", role ? role : "null", content ? content : "null");
}

void Display::SetTheme(const std::string& theme_name) {
    // 保存主题设置（即使无显示也要保存配置）
    current_theme_name_ = theme_name;
    Settings settings("display", true);
    settings.SetString("theme", theme_name);
}
