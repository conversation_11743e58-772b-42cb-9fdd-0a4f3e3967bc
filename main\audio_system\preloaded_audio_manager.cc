#include "preloaded_audio_manager.h"
#include "board.h"
#include "audio_codec.h"
#include <esp_log.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <cstring>
#include <algorithm>

#define TAG "PreloadedAudioManager"

// 预定义音频资源ID
namespace AudioIds {
    const char* STARTUP = "startup";
    const char* CONNECTED = "connected";
    const char* DISCONNECTED = "disconnected";
    const char* LOW_BATTERY = "low_battery";
    const char* ERROR = "error";
    const char* SUCCESS = "success";
    const char* EMERGENCY = "emergency";
    const char* BUTTON_CLICK = "button_click";
    
    const char* NIGHT_DRIVING = "night_driving";
    const char* SPEED_WARNING = "speed_warning";
    const char* REST_REMINDER = "rest_reminder";
    const char* WEATHER_ALERT = "weather_alert";
    
    const char* HOUR_CHIME = "hour_chime";
    const char* HALF_HOUR = "half_hour";
    const char* QUARTER_HOUR = "quarter_hour";
    const char* ALARM = "alarm";
}

// 临时音频数据（实际项目中应该从Flash读取）
// 这里使用简单的测试数据
static const uint8_t test_audio_startup[] = {
    0x4F, 0x70, 0x75, 0x73,  // "Opus" header
    0x48, 0x65, 0x6C, 0x6C, 0x6F, 0x20, 0x53, 0x74, 0x61, 0x72, 0x74, 0x75, 0x70  // "Hello Startup"
};

static const uint8_t test_audio_connected[] = {
    0x4F, 0x70, 0x75, 0x73,  // "Opus" header
    0x43, 0x6F, 0x6E, 0x6E, 0x65, 0x63, 0x74, 0x65, 0x64  // "Connected"
};

static const uint8_t test_audio_error[] = {
    0x4F, 0x70, 0x75, 0x73,  // "Opus" header
    0x45, 0x72, 0x72, 0x6F, 0x72  // "Error"
};

// 音频资源定义
const PreloadedAudioManager::AudioResource PreloadedAudioManager::audio_resources_[] = {
    // 系统音效
    {AudioIds::STARTUP, test_audio_startup, sizeof(test_audio_startup), SYSTEM_SOUNDS, 5, "System startup sound"},
    {AudioIds::CONNECTED, test_audio_connected, sizeof(test_audio_connected), SYSTEM_SOUNDS, 6, "Network connected sound"},
    {AudioIds::ERROR, test_audio_error, sizeof(test_audio_error), SYSTEM_SOUNDS, 8, "Error alert sound"},
    
    // 结束标记
    {nullptr, nullptr, 0, SYSTEM_SOUNDS, 0, nullptr}
};

PreloadedAudioManager& PreloadedAudioManager::GetInstance() {
    static PreloadedAudioManager instance;
    return instance;
}

PreloadedAudioManager::~PreloadedAudioManager() {
    StopPlayback();
}

void PreloadedAudioManager::Initialize() {
    ESP_LOGI(TAG, "Initializing PreloadedAudioManager");
    InitializeAudioIndex();
    ESP_LOGI(TAG, "PreloadedAudioManager initialized with %d audio resources", audio_index_.size());
}

void PreloadedAudioManager::InitializeAudioIndex() {
    audio_index_.clear();
    
    for (int i = 0; audio_resources_[i].id != nullptr; i++) {
        const AudioResource* resource = &audio_resources_[i];
        audio_index_[std::string(resource->id)] = resource;
        ESP_LOGI(TAG, "Registered audio: %s (%d bytes)", resource->id, resource->size);
    }
}

bool PreloadedAudioManager::PlayAudio(const std::string& audio_id, 
                                     std::function<void(bool success)> callback) {
    ESP_LOGI(TAG, "Request to play audio: %s", audio_id.c_str());
    
    // 检查音频是否存在
    auto it = audio_index_.find(audio_id);
    if (it == audio_index_.end()) {
        ESP_LOGE(TAG, "Audio not found: %s", audio_id.c_str());
        if (callback) callback(false);
        return false;
    }
    
    // 停止当前播放
    if (is_playing_) {
        ESP_LOGI(TAG, "Stopping current playback to play new audio");
        StopPlayback();
    }
    
    // 设置播放状态
    current_audio_id_ = audio_id;
    current_resource_ = it->second;
    current_completion_callback_ = callback;
    playback_position_ = 0;
    is_playing_ = true;
    is_paused_ = false;
    
    // 触发播放开始回调
    if (on_playback_started_) {
        on_playback_started_(audio_id);
    }
    
    // 开始流式播放
    bool success = StartStreamingPlayback(current_resource_);
    if (!success) {
        ESP_LOGE(TAG, "Failed to start streaming playback for: %s", audio_id.c_str());
        OnPlaybackFinished(false);
        return false;
    }
    
    return true;
}

bool PreloadedAudioManager::StartStreamingPlayback(const AudioResource* resource) {
    if (!resource || !resource->data || resource->size == 0) {
        ESP_LOGE(TAG, "Invalid audio resource");
        return false;
    }
    
    // 验证音频格式
    if (!IsValidP3Format(resource->data, resource->size)) {
        ESP_LOGW(TAG, "Audio format validation failed for: %s", resource->id);
        // 继续播放，可能是其他格式
    }
    
    // 创建流式播放任务
    BaseType_t result = xTaskCreate(
        StreamingTaskWrapper,
        "audio_streaming",
        4096,  // 4KB栈空间
        this,
        5,     // 优先级
        &streaming_task_handle_
    );
    
    if (result != pdPASS) {
        ESP_LOGE(TAG, "Failed to create streaming task");
        return false;
    }
    
    ESP_LOGI(TAG, "Started streaming playback for: %s (%d bytes)", resource->id, resource->size);
    return true;
}

void PreloadedAudioManager::StreamingTaskWrapper(void* parameter) {
    auto* manager = static_cast<PreloadedAudioManager*>(parameter);
    manager->StreamingPlaybackTask();
    vTaskDelete(nullptr);
}

void PreloadedAudioManager::StreamingPlaybackTask() {
    ESP_LOGI(TAG, "Streaming task started for: %s", current_resource_->id);
    
    bool success = true;
    const uint8_t* audio_data = GetP3AudioData(current_resource_->data);
    size_t audio_size = GetP3AudioSize(current_resource_->data, current_resource_->size);
    
    // 分块播放音频数据
    size_t bytes_played = 0;
    while (bytes_played < audio_size && is_playing_ && !is_paused_) {
        size_t chunk_size = std::min(STREAM_BUFFER_SIZE, audio_size - bytes_played);
        
        // 复制数据到缓冲区
        memcpy(stream_buffer_, audio_data + bytes_played, chunk_size);
        
        // 播放音频块
        if (!PlayAudioChunk(stream_buffer_, chunk_size)) {
            ESP_LOGE(TAG, "Failed to play audio chunk at position %d", bytes_played);
            success = false;
            break;
        }
        
        bytes_played += chunk_size;
        playback_position_ = bytes_played;
        
        // 触发进度回调
        if (on_playback_progress_) {
            uint32_t position_ms = (bytes_played * 1000) / (16000 * 2); // 假设16kHz, 16bit
            uint32_t duration_ms = CalculateAudioDuration(current_resource_);
            on_playback_progress_(current_audio_id_, position_ms, duration_ms);
        }
        
        // 短暂延时，避免占用过多CPU
        vTaskDelay(pdMS_TO_TICKS(10));
    }
    
    ESP_LOGI(TAG, "Streaming task completed for: %s (success=%d)", current_resource_->id, success);
    
    // 播放完成
    OnPlaybackFinished(success && bytes_played == audio_size);
}

bool PreloadedAudioManager::PlayAudioChunk(const uint8_t* data, size_t size) {
    auto codec = GetAudioCodec();
    if (!codec) {
        ESP_LOGE(TAG, "AudioCodec not available");
        return false;
    }
    
    // 确保音频输出已启用
    if (!codec->output_enabled()) {
        codec->EnableOutput(true);
        ESP_LOGI(TAG, "Enabled audio output");
    }
    
    // 将数据转换为int16_t格式（简化处理）
    std::vector<int16_t> audio_samples;
    audio_samples.reserve(size / 2);
    
    for (size_t i = 0; i < size - 1; i += 2) {
        int16_t sample = (data[i + 1] << 8) | data[i];  // 小端序
        audio_samples.push_back(sample);
    }
    
    // 播放音频样本 - 使用公共接口
    codec->OutputData(audio_samples);
    return true;
}

void PreloadedAudioManager::StopPlayback() {
    if (!is_playing_) {
        return;
    }
    
    ESP_LOGI(TAG, "Stopping playback: %s", current_audio_id_.c_str());
    
    is_playing_ = false;
    is_paused_ = false;
    
    // 等待流式任务完成
    if (streaming_task_handle_) {
        // 任务会自动检测is_playing_状态并退出
        streaming_task_handle_ = nullptr;
    }
    
    // 重置状态
    current_audio_id_.clear();
    current_resource_ = nullptr;
    playback_position_ = 0;
}

void PreloadedAudioManager::PausePlayback() {
    if (is_playing_ && !is_paused_) {
        ESP_LOGI(TAG, "Pausing playback: %s", current_audio_id_.c_str());
        is_paused_ = true;
    }
}

void PreloadedAudioManager::ResumePlayback() {
    if (is_playing_ && is_paused_) {
        ESP_LOGI(TAG, "Resuming playback: %s", current_audio_id_.c_str());
        is_paused_ = false;
    }
}

void PreloadedAudioManager::OnPlaybackFinished(bool success) {
    std::string finished_audio_id = current_audio_id_;
    
    // 重置播放状态
    is_playing_ = false;
    is_paused_ = false;
    streaming_task_handle_ = nullptr;
    
    ESP_LOGI(TAG, "Playback finished: %s (success=%d)", finished_audio_id.c_str(), success);
    
    // 触发完成回调
    if (on_playback_completed_) {
        on_playback_completed_(finished_audio_id, success);
    }
    
    if (current_completion_callback_) {
        current_completion_callback_(success);
        current_completion_callback_ = nullptr;
    }
    
    // 清理状态
    current_audio_id_.clear();
    current_resource_ = nullptr;
    playback_position_ = 0;
}

bool PreloadedAudioManager::IsAudioAvailable(const std::string& audio_id) const {
    return audio_index_.find(audio_id) != audio_index_.end();
}

std::vector<std::string> PreloadedAudioManager::GetAvailableAudioList() const {
    std::vector<std::string> audio_list;
    for (const auto& pair : audio_index_) {
        audio_list.push_back(pair.first);
    }
    return audio_list;
}

std::vector<std::string> PreloadedAudioManager::GetAudioListByCategory(AudioCategory category) const {
    std::vector<std::string> audio_list;
    for (const auto& pair : audio_index_) {
        if (pair.second->category == category) {
            audio_list.push_back(pair.first);
        }
    }
    return audio_list;
}

PreloadedAudioManager::PlaybackStatus PreloadedAudioManager::GetPlaybackStatus() const {
    PlaybackStatus status;
    status.is_playing = is_playing_;
    status.current_audio_id = current_audio_id_;
    status.bytes_played = playback_position_;
    status.total_bytes = current_resource_ ? current_resource_->size : 0;
    status.duration_ms = current_resource_ ? CalculateAudioDuration(current_resource_) : 0;
    status.position_ms = current_resource_ ? (playback_position_ * 1000) / (16000 * 2) : 0;
    return status;
}

const PreloadedAudioManager::AudioResource* PreloadedAudioManager::GetAudioResource(const std::string& audio_id) const {
    auto it = audio_index_.find(audio_id);
    return (it != audio_index_.end()) ? it->second : nullptr;
}

AudioCodec* PreloadedAudioManager::GetAudioCodec() const {
    return Board::GetInstance().GetAudioCodec();
}

bool PreloadedAudioManager::IsValidP3Format(const uint8_t* data, size_t size) const {
    if (size < 4) return false;
    
    // 检查是否以"Opus"开头（简化的P3格式检查）
    return (data[0] == 'O' && data[1] == 'p' && data[2] == 'u' && data[3] == 's');
}

size_t PreloadedAudioManager::GetP3HeaderSize(const uint8_t* data) const {
    // 简化实现：假设4字节头部
    return 4;
}

const uint8_t* PreloadedAudioManager::GetP3AudioData(const uint8_t* data) const {
    return data + GetP3HeaderSize(data);
}

size_t PreloadedAudioManager::GetP3AudioSize(const uint8_t* data, size_t total_size) const {
    return total_size - GetP3HeaderSize(data);
}

uint32_t PreloadedAudioManager::CalculateAudioDuration(const AudioResource* resource) const {
    if (!resource) return 0;
    
    // 简化计算：假设16kHz采样率，16bit深度
    size_t audio_size = GetP3AudioSize(resource->data, resource->size);
    return (audio_size * 1000) / (16000 * 2);  // 毫秒
}

void PreloadedAudioManager::OnPlaybackStarted(std::function<void(const std::string&)> callback) {
    on_playback_started_ = callback;
}

void PreloadedAudioManager::OnPlaybackCompleted(std::function<void(const std::string&, bool)> callback) {
    on_playback_completed_ = callback;
}

void PreloadedAudioManager::OnPlaybackProgress(std::function<void(const std::string&, uint32_t, uint32_t)> callback) {
    on_playback_progress_ = callback;
}
