#include "direct_tts_handler.h"
#include "protocols/protocol.h"
#include <esp_log.h>
#include <cJSON.h>
#include <time.h>
#include <random>

#define TAG "DirectTTSHandler"

// 静态文本模板定义
const std::map<DirectTTSHandler::TTSTemplate, std::vector<std::string>> DirectTTSHandler::text_templates_ = {
    {DRIVING_SAFETY, {
        "请注意行车安全",
        "保持安全车距",
        "注意观察路况",
        "安全驾驶，平安回家",
        "请系好安全带"
    }},
    {TIME_ANNOUNCEMENT, {
        "现在时间是{time}",
        "当前时间{time}",
        "时间提醒：{time}"
    }},
    {SYSTEM_NOTIFICATION, {
        "系统提醒：{message}",
        "通知：{message}",
        "{message}"
    }},
    {CUSTOM_TEXT, {
        "{text}"
    }}
};

DirectTTSHandler::DirectTTSHandler(Protocol* protocol)
    : protocol_(protocol), is_playing_(false) {
    ESP_LOGI(TAG, "DirectTTSHandler initialized");
}

DirectTTSHandler::~DirectTTSHandler() {
    StopPlayback();
    ESP_LOGI(TAG, "DirectTTSHandler destroyed");
}

void DirectTTSHandler::PlayText(const std::string& text,
                               TTSTemplate template_type,
                               std::function<void(bool success)> callback) {
    if (!protocol_) {
        ESP_LOGE(TAG, "Protocol not initialized");
        if (callback) callback(false);
        return;
    }

    if (is_playing_) {
        ESP_LOGW(TAG, "Already playing, stopping current playback");
        StopPlayback();
    }

    ESP_LOGI(TAG, "Playing text (template=%d): %s", template_type, text.c_str());

    current_text_ = text;
    current_completion_callback_ = callback;
    is_playing_ = true;

    // 触发播放开始回调
    if (on_playback_started_) {
        on_playback_started_(text);
    }

    // 首先建立音频通道，然后发送直接TTS请求
    if (!protocol_->IsAudioChannelOpened()) {
        ESP_LOGI(TAG, "Opening audio channel for direct TTS");
        if (!protocol_->OpenAudioChannel()) {
            ESP_LOGE(TAG, "Failed to open audio channel for direct TTS");
            OnTTSResponse(false);
            return;
        }
    }

    // 发送直接TTS请求
    SendDirectTTSRequest(text);
}

void DirectTTSHandler::PlayDrivingSafetyTip() {
    std::string tip = GetRandomTemplate(DRIVING_SAFETY);
    PlayText(tip, DRIVING_SAFETY);
}

void DirectTTSHandler::PlayTimeAnnouncement() {
    std::string announcement = FormatTimeAnnouncement();
    PlayText(announcement, TIME_ANNOUNCEMENT);
}

void DirectTTSHandler::PlaySystemNotification(const std::string& message) {
    std::string notification = GetRandomTemplate(SYSTEM_NOTIFICATION);
    // 替换占位符
    size_t pos = notification.find("{message}");
    if (pos != std::string::npos) {
        notification.replace(pos, 9, message);
    }
    PlayText(notification, SYSTEM_NOTIFICATION);
}

void DirectTTSHandler::StopPlayback() {
    if (is_playing_) {
        ESP_LOGI(TAG, "Stopping playback");
        is_playing_ = false;
        current_text_.clear();

        // 触发完成回调
        if (current_completion_callback_) {
            current_completion_callback_(false);
            current_completion_callback_ = nullptr;
        }
    }
}

void DirectTTSHandler::OnPlaybackStarted(std::function<void(const std::string& text)> callback) {
    on_playback_started_ = callback;
}

void DirectTTSHandler::OnPlaybackCompleted(std::function<void(const std::string& text, bool success)> callback) {
    on_playback_completed_ = callback;
}

void DirectTTSHandler::SendDirectTTSRequest(const std::string& text) {
    std::string mcp_message = BuildMCPMessage(text);

    protocol_->SendMcpMessage(mcp_message);
    ESP_LOGI(TAG, "Direct TTS request sent, waiting for server response...");

    // 不再立即模拟成功响应，等待服务器的真实响应
    // 响应将通过Application的OnIncomingJson回调处理
}

void DirectTTSHandler::OnTTSResponse(bool success) {
    ESP_LOGI(TAG, "TTS response received: %s", success ? "success" : "failed");

    is_playing_ = false;

    // 触发完成回调
    if (on_playback_completed_) {
        on_playback_completed_(current_text_, success);
    }

    if (current_completion_callback_) {
        current_completion_callback_(success);
        current_completion_callback_ = nullptr;
    }

    current_text_.clear();
}

void DirectTTSHandler::HandleTTSResponse(const std::string& state, const std::string& text) {
    ESP_LOGI(TAG, "Handling TTS response: state=%s, text=%s", state.c_str(), text.c_str());

    if (state == "start") {
        ESP_LOGI(TAG, "TTS playback started");
        // TTS开始播放，保持is_playing_状态
    } else if (state == "stop") {
        ESP_LOGI(TAG, "TTS playback completed");
        // TTS播放完成，调用成功回调
        OnTTSResponse(true);
    } else if (state == "sentence_start") {
        if (!text.empty()) {
            ESP_LOGI(TAG, "TTS sentence: %s", text.c_str());
        }
    } else {
        ESP_LOGW(TAG, "Unknown TTS state: %s", state.c_str());
    }
}

std::string DirectTTSHandler::GetRandomTemplate(TTSTemplate template_type) const {
    auto it = text_templates_.find(template_type);
    if (it == text_templates_.end() || it->second.empty()) {
        return "模板未找到";
    }

    const auto& templates = it->second;
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, templates.size() - 1);

    return templates[dis(gen)];
}

std::string DirectTTSHandler::FormatTimeAnnouncement() const {
    time_t now = time(NULL);
    struct tm* timeinfo = localtime(&now);

    char time_str[64];
    strftime(time_str, sizeof(time_str), "%H点%M分", timeinfo);

    std::string template_str = GetRandomTemplate(TIME_ANNOUNCEMENT);
    size_t pos = template_str.find("{time}");
    if (pos != std::string::npos) {
        template_str.replace(pos, 6, time_str);
    }

    return template_str;
}

std::string DirectTTSHandler::BuildMCPMessage(const std::string& text) const {
    cJSON* root = cJSON_CreateObject();
    cJSON* jsonrpc = cJSON_CreateString("2.0");
    cJSON* method = cJSON_CreateString("tools/call");
    cJSON* id = cJSON_CreateNumber(1);

    cJSON* params = cJSON_CreateObject();
    cJSON* name = cJSON_CreateString("direct_tts");
    cJSON* arguments = cJSON_CreateObject();
    cJSON* text_param = cJSON_CreateString(text.c_str());

    cJSON_AddItemToObject(arguments, "text", text_param);
    cJSON_AddItemToObject(params, "name", name);
    cJSON_AddItemToObject(params, "arguments", arguments);

    cJSON_AddItemToObject(root, "jsonrpc", jsonrpc);
    cJSON_AddItemToObject(root, "method", method);
    cJSON_AddItemToObject(root, "params", params);
    cJSON_AddItemToObject(root, "id", id);

    char* json_string = cJSON_Print(root);
    std::string result(json_string);

    free(json_string);
    cJSON_Delete(root);

    return result;
}
