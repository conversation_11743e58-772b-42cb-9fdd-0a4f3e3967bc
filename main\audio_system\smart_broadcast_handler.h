#ifndef SMART_BROADCAST_HANDLER_H
#define SMART_BROADCAST_HANDLER_H

#include <string>
#include <functional>

class Protocol;

/**
 * 智能播报处理器
 * 负责处理需要大模型处理的智能交互播报
 * 基于现有的STT协议发送查询给服务器
 */
class SmartBroadcastHandler {
public:
    enum QueryType {
        WEATHER_QUERY,      // 天气查询
        NEWS_BRIEF,         // 新闻播报
        GENERAL_QUERY,      // 通用查询
        CUSTOM_QUERY        // 自定义查询
    };

    SmartBroadcastHandler(Protocol* protocol);
    ~SmartBroadcastHandler();
    
    /**
     * 发送智能查询
     * @param query 查询文本
     * @param query_type 查询类型
     * @param callback 播放完成回调
     */
    void SendQuery(const std::string& query, 
                   QueryType query_type = CUSTOM_QUERY,
                   std::function<void(bool success)> callback = nullptr);
    
    /**
     * 预定义查询方法
     */
    void QueryWeather();
    void QueryNews();
    void SendCustomQuery(const std::string& query);
    
    /**
     * 停止当前播放
     */
    void StopPlayback();
    
    /**
     * 状态查询
     */
    bool IsPlaying() const { return is_playing_; }
    
    /**
     * 回调设置
     */
    void OnPlaybackStarted(std::function<void(const std::string& query)> callback);
    void OnPlaybackCompleted(std::function<void(const std::string& query, bool success)> callback);

    /**
     * 处理服务器TTS响应
     */
    void HandleTTSResponse(const std::string& state, const std::string& text = "");

private:
    Protocol* protocol_;
    bool is_playing_ = false;
    std::string current_query_;
    
    // 回调函数
    std::function<void(const std::string&)> on_playback_started_;
    std::function<void(const std::string&, bool)> on_playback_completed_;
    std::function<void(bool)> current_completion_callback_;
    
    // 核心方法
    void SendSTTMessage(const std::string& query, QueryType query_type);
    void OnSmartResponse(bool success);
    std::string BuildSTTMessage(const std::string& query, QueryType query_type) const;
    std::string GetContextForQueryType(QueryType query_type) const;
};

#endif // SMART_BROADCAST_HANDLER_H
