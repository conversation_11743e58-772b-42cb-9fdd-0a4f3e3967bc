#include "unified_audio_manager.h"
#include "preloaded_audio_manager.h"
#include "direct_tts_handler.h"
#include "smart_broadcast_handler.h"
#include "protocols/protocol.h"
#include <esp_log.h>
#include <esp_timer.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>

#define TAG "UnifiedAudioManager"

UnifiedAudioManager& UnifiedAudioManager::GetInstance() {
    static UnifiedAudioManager instance;
    return instance;
}

UnifiedAudioManager::~UnifiedAudioManager() {
    StopCurrentPlayback();
    ClearQueue();
}

void UnifiedAudioManager::Initialize(Protocol* protocol) {
    ESP_LOGI(TAG, "Initializing UnifiedAudioManager");
    
    protocol_ = protocol;
    
    // 初始化预存音频管理器（优先实现）
    preloaded_manager_ = &PreloadedAudioManager::GetInstance();
    preloaded_manager_->Initialize();
    
    // 设置预存音频管理器的回调
    preloaded_manager_->OnPlaybackStarted([this](const std::string& audio_id) {
        ESP_LOGI(TAG, "Preloaded audio started: %s", audio_id.c_str());
        if (on_playback_started_) {
            on_playback_started_(audio_id, SYSTEM_ALERT);  // 假设预存音频都是系统提醒
        }
    });
    
    preloaded_manager_->OnPlaybackCompleted([this](const std::string& audio_id, bool success) {
        ESP_LOGI(TAG, "Preloaded audio completed: %s (success=%d)", audio_id.c_str(), success);
        OnRequestCompleted(success);
    });
    
    // TODO: 初始化其他处理器（阶段二实现）
    // direct_tts_handler_ = std::make_unique<DirectTTSHandler>(protocol);
    // smart_handler_ = std::make_unique<SmartBroadcastHandler>(protocol);
    
    SetPlaybackState(IDLE);
    ESP_LOGI(TAG, "UnifiedAudioManager initialized successfully");
}

void UnifiedAudioManager::PlayAudio(const std::string& content, 
                                   AudioScenario scenario,
                                   PlayMode mode,
                                   int priority,
                                   std::function<void(bool success)> callback) {
    ESP_LOGI(TAG, "PlayAudio request: content='%s', scenario=%d, mode=%d, priority=%d", 
             content.c_str(), scenario, mode, priority);
    
    // 创建音频请求
    AudioRequest request;
    request.content = content;
    request.scenario = scenario;
    request.mode = (mode == AUTO_SELECT) ? SelectOptimalMode(content, scenario) : mode;
    request.priority = priority;
    request.timestamp = esp_timer_get_time();
    request.completion_callback = callback;
    
    // 添加到队列
    {
        std::lock_guard<std::mutex> lock(queue_mutex_);
        request_queue_.push(request);
    }
    
    ESP_LOGI(TAG, "Audio request queued (queue size: %d)", GetQueueSize());
    
    // 如果当前空闲，立即处理
    if (current_state_ == IDLE) {
        ProcessNextRequest();
    }
}

void UnifiedAudioManager::PlayWeatherQuery(const std::string& query) {
    PlayAudio(query, WEATHER_QUERY, AUTO_SELECT, 8);
}

void UnifiedAudioManager::PlayDrivingSafety(const std::string& tip) {
    PlayAudio(tip, DRIVING_SAFETY, AUTO_SELECT, 7);
}

void UnifiedAudioManager::PlaySystemAlert(const std::string& alert_id) {
    PlayAudio(alert_id, SYSTEM_ALERT, FORCE_PRELOADED, 9);
}

void UnifiedAudioManager::PlayTimeAnnouncement() {
    time_t now = time(NULL);
    struct tm* timeinfo = localtime(&now);
    char time_str[64];
    strftime(time_str, sizeof(time_str), "现在是%H点%M分", timeinfo);
    PlayAudio(std::string(time_str), TIME_ANNOUNCEMENT, AUTO_SELECT, 6);
}

void UnifiedAudioManager::PlayEmergencyWarning(const std::string& warning_id) {
    // 紧急警告具有最高优先级，立即停止当前播放
    StopCurrentPlayback();
    PlayAudio(warning_id, EMERGENCY_WARNING, FORCE_PRELOADED, 10);
}

void UnifiedAudioManager::StopCurrentPlayback() {
    if (current_state_ == IDLE) {
        return;
    }
    
    ESP_LOGI(TAG, "Stopping current playback");
    
    // 停止相应的播放器
    switch (current_request_.mode) {
        case FORCE_PRELOADED:
            if (preloaded_manager_) {
                preloaded_manager_->StopPlayback();
            }
            break;
        case FORCE_DIRECT_TTS:
            // TODO: 实现DirectTTS停止
            break;
        case FORCE_SMART:
            // TODO: 实现Smart停止
            break;
        default:
            break;
    }
    
    SetPlaybackState(IDLE);
}

void UnifiedAudioManager::PausePlayback() {
    if (current_state_ != PLAYING) {
        return;
    }
    
    ESP_LOGI(TAG, "Pausing playback");
    
    switch (current_request_.mode) {
        case FORCE_PRELOADED:
            if (preloaded_manager_) {
                preloaded_manager_->PausePlayback();
            }
            break;
        default:
            ESP_LOGW(TAG, "Pause not supported for current play mode");
            break;
    }
    
    SetPlaybackState(PAUSED);
}

void UnifiedAudioManager::ResumePlayback() {
    if (current_state_ != PAUSED) {
        return;
    }
    
    ESP_LOGI(TAG, "Resuming playback");
    
    switch (current_request_.mode) {
        case FORCE_PRELOADED:
            if (preloaded_manager_) {
                preloaded_manager_->ResumePlayback();
            }
            break;
        default:
            ESP_LOGW(TAG, "Resume not supported for current play mode");
            break;
    }
    
    SetPlaybackState(PLAYING);
}

void UnifiedAudioManager::ClearQueue() {
    std::lock_guard<std::mutex> lock(queue_mutex_);
    
    // 清空队列
    std::priority_queue<AudioRequest> empty_queue;
    request_queue_.swap(empty_queue);
    
    ESP_LOGI(TAG, "Audio queue cleared");
}

size_t UnifiedAudioManager::GetQueueSize() const {
    std::lock_guard<std::mutex> lock(queue_mutex_);
    return request_queue_.size();
}

UnifiedAudioManager::PlayMode UnifiedAudioManager::SelectOptimalMode(const std::string& content, AudioScenario scenario) {
    // 检查系统状态
    bool network_available = IsNetworkAvailable();
    bool memory_sufficient = IsMemorySufficient(15000);  // 15KB阈值
    
    ESP_LOGI(TAG, "Selecting optimal mode: network=%d, memory=%d, scenario=%d", 
             network_available, memory_sufficient, scenario);
    
    switch (scenario) {
        case EMERGENCY_WARNING:
        case SYSTEM_ALERT:
            // 紧急情况优先使用本地音频
            return FORCE_PRELOADED;
            
        case WEATHER_QUERY:
        case NEWS_BRIEF:
            // 智能查询优先，但需要网络
            if (network_available && memory_sufficient) {
                return FORCE_SMART;
            } else {
                // 降级到预存音频
                ESP_LOGW(TAG, "Network/memory insufficient, fallback to preloaded audio");
                return FORCE_PRELOADED;
            }
            
        case DRIVING_SAFETY:
        case TIME_ANNOUNCEMENT:
            // 直接TTS优先，但需要网络
            if (network_available && memory_sufficient) {
                return FORCE_DIRECT_TTS;
            } else {
                // 降级到预存音频
                ESP_LOGW(TAG, "Network/memory insufficient, fallback to preloaded audio");
                return FORCE_PRELOADED;
            }
            
        case CUSTOM_TEXT:
            // 根据内容长度判断
            if (content.length() > 50 && network_available && memory_sufficient) {
                return FORCE_SMART;
            } else if (network_available && memory_sufficient) {
                return FORCE_DIRECT_TTS;
            } else {
                return FORCE_PRELOADED;
            }
    }
    
    // 默认使用最可靠的方式
    return FORCE_PRELOADED;
}

void UnifiedAudioManager::ProcessNextRequest() {
    std::lock_guard<std::mutex> lock(queue_mutex_);
    
    if (request_queue_.empty()) {
        ESP_LOGI(TAG, "No more requests in queue");
        SetPlaybackState(IDLE);
        return;
    }
    
    // 获取优先级最高的请求
    current_request_ = request_queue_.top();
    request_queue_.pop();
    
    ESP_LOGI(TAG, "Processing audio request: content='%s', mode=%d, priority=%d", 
             current_request_.content.c_str(), current_request_.mode, current_request_.priority);
    
    // 处理请求
    ProcessAudioRequest(current_request_);
}

void UnifiedAudioManager::ProcessAudioRequest(const AudioRequest& request) {
    SetPlaybackState(PREPARING);
    
    bool success = false;
    
    switch (request.mode) {
        case FORCE_PRELOADED:
            if (preloaded_manager_) {
                success = preloaded_manager_->PlayAudio(request.content, 
                    [this](bool result) {
                        // 回调在OnRequestCompleted中处理
                    });
            }
            break;
            
        case FORCE_DIRECT_TTS:
            // TODO: 实现DirectTTS播放
            ESP_LOGW(TAG, "DirectTTS not implemented yet, fallback to preloaded");
            if (preloaded_manager_) {
                success = preloaded_manager_->PlayAudio("error");
            }
            break;
            
        case FORCE_SMART:
            // TODO: 实现Smart播放
            ESP_LOGW(TAG, "Smart broadcast not implemented yet, fallback to preloaded");
            if (preloaded_manager_) {
                success = preloaded_manager_->PlayAudio("error");
            }
            break;
            
        default:
            ESP_LOGE(TAG, "Unknown play mode: %d", request.mode);
            break;
    }
    
    if (success) {
        SetPlaybackState(PLAYING);
    } else {
        ESP_LOGE(TAG, "Failed to start playback");
        OnRequestCompleted(false);
    }
}

void UnifiedAudioManager::SetPlaybackState(PlaybackState state) {
    if (current_state_ != state) {
        PlaybackState old_state = current_state_;
        current_state_ = state;
        
        ESP_LOGI(TAG, "Playback state changed: %d -> %d", old_state, state);
        
        if (on_state_changed_) {
            on_state_changed_(state);
        }
    }
}

void UnifiedAudioManager::OnRequestCompleted(bool success) {
    ESP_LOGI(TAG, "Audio request completed (success=%d)", success);
    
    // 触发完成回调
    if (on_playback_completed_) {
        on_playback_completed_(success);
    }
    
    if (current_request_.completion_callback) {
        current_request_.completion_callback(success);
    }
    
    // 处理下一个请求
    ProcessNextRequest();
}

bool UnifiedAudioManager::IsNetworkAvailable() const {
    return protocol_ && protocol_->IsAudioChannelOpened();
}

bool UnifiedAudioManager::IsMemorySufficient(size_t required_memory) const {
    size_t available = GetAvailableMemory();
    return available >= required_memory;
}

size_t UnifiedAudioManager::GetAvailableMemory() const {
    return heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
}

void UnifiedAudioManager::OnPlaybackStateChanged(std::function<void(PlaybackState)> callback) {
    on_state_changed_ = callback;
}

void UnifiedAudioManager::OnPlaybackStarted(std::function<void(const std::string&, AudioScenario)> callback) {
    on_playback_started_ = callback;
}

void UnifiedAudioManager::OnPlaybackCompleted(std::function<void(bool)> callback) {
    on_playback_completed_ = callback;
}
