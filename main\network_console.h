#ifndef NETWORK_CONSOLE_H
#define NETWORK_CONSOLE_H

#include <esp_console.h>
#include <esp_log.h>

class NetworkConsole {
public:
    static NetworkConsole& GetInstance();
    
    void Initialize();
    void RegisterNetworkCommands();
    
private:
    NetworkConsole() = default;
    ~NetworkConsole() = default;
    
    // 禁止拷贝和赋值
    NetworkConsole(const NetworkConsole&) = delete;
    NetworkConsole& operator=(const NetworkConsole&) = delete;
    
    // 命令处理函数
    static int CmdSwitchTo4G(int argc, char** argv);
    static int CmdSwitchToWiFi(int argc, char** argv);
    static int CmdNetworkStatus(int argc, char** argv);
    static int CmdHelp(int argc, char** argv);
    static int CmdReboot(int argc, char** argv);

    // 音频播报测试命令
    static int CmdBroadcastText(int argc, char** argv);
    static int CmdBroadcastWeather(int argc, char** argv);
    static int CmdBroadcastTime(int argc, char** argv);
    static int CmdPlayAudio(int argc, char** argv);
    static int CmdAudioHelp(int argc, char** argv);

    // 智能播报命令（需要大模型处理）
    static int CmdSmartWeather(int argc, char** argv);
    static int CmdSmartTime(int argc, char** argv);

    // 直接TTS命令（不需要大模型）
    static int CmdDirectTTS(int argc, char** argv);

    // 本地音频播放命令
    static int CmdPlayLocal(int argc, char** argv);
    
    bool initialized_ = false;
};

#endif // NETWORK_CONSOLE_H
