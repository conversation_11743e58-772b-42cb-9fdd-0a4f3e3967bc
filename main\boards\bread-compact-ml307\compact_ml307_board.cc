#include "dual_network_board.h"
#include "audio_codecs/no_audio_codec.h"
#include "display/display.h"
#include "system_reset.h"
#include "application.h"
#include "button.h"
#include "config.h"
#include "mcp_server.h"
#include "lamp_controller.h"
#include "iot/thing_manager.h"
#include "led/single_led.h"
#include "assets/lang_config.h"

#include <esp_log.h>
#include <wifi_station.h>
#include <driver/rtc_io.h>  // 用于ML307电源控制
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>  // 用于vTaskDelay
#include "broadcast_manager.h"  // 用于播报功能
#include "application.h"  // 用于音频测试

#define TAG "CompactMl307Board"

class CompactMl307Board : public DualNetworkBoard {
private:
    Display* display_ = nullptr;
    Button boot_button_;
    Button touch_button_;
    Button volume_up_button_;
    Button volume_down_button_;

    void InitializeDisplay() {
        ESP_LOGI(TAG, "Display disabled - using NoDisplay");
        display_ = CreateNoDisplay();
    }

    void InitializeMl307Power() {
        // 初始化ML307电源控制引脚 - 控制ML307的EN引脚
        // GPIO21 连接到 ML307的EN引脚，拉高使能4G模块
        rtc_gpio_init(ML307_POWER_PIN);
        rtc_gpio_set_direction(ML307_POWER_PIN, RTC_GPIO_MODE_OUTPUT_ONLY);
        rtc_gpio_set_level(ML307_POWER_PIN, 1);  // 使能ML307模块
        ESP_LOGI(TAG, "ML307 EN pin control initialized - GPIO%d set to HIGH", ML307_POWER_PIN);

        // 等待ML307模块启动，给足够的时间让模块完全启动
        ESP_LOGI(TAG, "Waiting for ML307 module to start up...");
        vTaskDelay(pdMS_TO_TICKS(3000));  // 等待3秒
        ESP_LOGI(TAG, "ML307 startup delay completed");
    }

    void InitializeBroadcastFeatures() {
        ESP_LOGI(TAG, "Initializing broadcast features for ML307 board");

        // 延迟启动播报功能，等待网络连接稳定
        esp_timer_create_args_t timer_args = {
            .callback = [](void* arg) {
                auto* board = (CompactMl307Board*)arg;
                board->StartBroadcastServices();
            },
            .arg = this,
            .dispatch_method = ESP_TIMER_TASK,
            .name = "broadcast_init_timer",
            .skip_unhandled_events = true
        };

        esp_timer_handle_t init_timer;
        esp_timer_create(&timer_args, &init_timer);
        esp_timer_start_once(init_timer, 30 * 1000000ULL); // 30秒后启动
    }

    void StartBroadcastServices() {
        ESP_LOGI(TAG, "Starting broadcast services");

        auto& broadcast_manager = BroadcastManager::GetInstance();

        // 启动天气播报 - 每12小时播报一次
        broadcast_manager.StartWeatherBroadcast(12);

        // 启动整点报时
        broadcast_manager.StartHourlyTimeBroadcast();

        // 启动行车技巧播报 - 每30分钟播报一次
        broadcast_manager.StartDrivingTipsBroadcast(30);

        // 设置播报状态回调
        broadcast_manager.OnBroadcastStart([](const std::string& text, BroadcastManager::BroadcastType type, BroadcastManager::AudioPlayMode mode) {
            ESP_LOGI(TAG, "Broadcast started (type=%d, mode=%d): %s", type, mode, text.c_str());
        });

        broadcast_manager.OnBroadcastEnd([](BroadcastManager::BroadcastType type, BroadcastManager::AudioPlayMode mode) {
            ESP_LOGI(TAG, "Broadcast ended (type=%d, mode=%d)", type, mode);
        });

        ESP_LOGI(TAG, "All broadcast services started successfully");

        // 测试预存音频播放功能
        TestPreloadedAudioPlayback();
    }

    void TestPreloadedAudioPlayback() {
        ESP_LOGI(TAG, "Testing preloaded audio playback functionality");

        // 延迟5秒后开始测试，确保系统完全初始化
        esp_timer_create_args_t timer_args = {
            .callback = [](void* arg) {
                auto* board = (CompactMl307Board*)arg;
                board->RunAudioTests();
            },
            .arg = this,
            .dispatch_method = ESP_TIMER_TASK,
            .name = "audio_test_timer",
            .skip_unhandled_events = true
        };

        esp_timer_handle_t test_timer;
        esp_timer_create(&timer_args, &test_timer);
        esp_timer_start_once(test_timer, 5 * 1000000ULL); // 5秒后执行
    }

    void RunAudioTests() {
        ESP_LOGI(TAG, "Running audio system tests");

        auto& app = Application::GetInstance();

        // 测试1：播放系统启动音
        ESP_LOGI(TAG, "Test 1: Playing startup sound");
        app.PlaySystemAlert("startup");

        // 延迟3秒后播放下一个测试
        esp_timer_create_args_t timer_args = {
            .callback = [](void* arg) {
                auto* board = (CompactMl307Board*)arg;
                board->RunAudioTest2();
            },
            .arg = this,
            .dispatch_method = ESP_TIMER_TASK,
            .name = "audio_test2_timer",
            .skip_unhandled_events = true
        };

        esp_timer_handle_t test_timer;
        esp_timer_create(&timer_args, &test_timer);
        esp_timer_start_once(test_timer, 3 * 1000000ULL); // 3秒后执行
    }

    void RunAudioTest2() {
        ESP_LOGI(TAG, "Test 2: Playing connected sound");
        auto& app = Application::GetInstance();
        app.PlaySystemAlert("connected");

        // 延迟3秒后播放下一个测试
        esp_timer_create_args_t timer_args = {
            .callback = [](void* arg) {
                auto* board = (CompactMl307Board*)arg;
                board->RunAudioTest3();
            },
            .arg = this,
            .dispatch_method = ESP_TIMER_TASK,
            .name = "audio_test3_timer",
            .skip_unhandled_events = true
        };

        esp_timer_handle_t test_timer;
        esp_timer_create(&timer_args, &test_timer);
        esp_timer_start_once(test_timer, 3 * 1000000ULL); // 3秒后执行
    }

    void RunAudioTest3() {
        ESP_LOGI(TAG, "Test 3: Playing error sound");
        auto& app = Application::GetInstance();
        app.PlaySystemAlert("error");

        ESP_LOGI(TAG, "All audio tests completed");
    }

    void InitializeButtons() {
        boot_button_.OnClick([this]() {
            auto& app = Application::GetInstance();
            if (GetNetworkType() == NetworkType::WIFI) {
                if (app.GetDeviceState() == kDeviceStateStarting && !WifiStation::GetInstance().IsConnected()) {
                    // cast to WifiBoard
                    auto& wifi_board = static_cast<WifiBoard&>(GetCurrentBoard());
                    wifi_board.ResetWifiConfiguration();
                }
            }
            app.ToggleChatState();
        });
        boot_button_.OnDoubleClick([this]() {
            auto& app = Application::GetInstance();
            if (app.GetDeviceState() == kDeviceStateStarting || app.GetDeviceState() == kDeviceStateWifiConfiguring) {
                SwitchNetworkType();
            }
        });

        touch_button_.OnPressDown([this]() {
            Application::GetInstance().StartListening();
        });
        touch_button_.OnPressUp([this]() {
            Application::GetInstance().StopListening();
        });

        volume_up_button_.OnClick([this]() {
            auto codec = GetAudioCodec();
            auto volume = codec->output_volume() + 10;
            if (volume > 100) {
                volume = 100;
            }
            codec->SetOutputVolume(volume);
            GetDisplay()->ShowNotification(Lang::Strings::VOLUME + std::to_string(volume));
        });

        volume_up_button_.OnLongPress([this]() {
            GetAudioCodec()->SetOutputVolume(100);
            GetDisplay()->ShowNotification(Lang::Strings::MAX_VOLUME);
        });

        volume_down_button_.OnClick([this]() {
            auto codec = GetAudioCodec();
            auto volume = codec->output_volume() - 10;
            if (volume < 0) {
                volume = 0;
            }
            codec->SetOutputVolume(volume);
            GetDisplay()->ShowNotification(Lang::Strings::VOLUME + std::to_string(volume));
        });

        volume_down_button_.OnLongPress([this]() {
            GetAudioCodec()->SetOutputVolume(0);
            GetDisplay()->ShowNotification(Lang::Strings::MUTED);
        });
    }

    // 物联网初始化，添加对 AI 可见设备
    void InitializeIot() {
#if CONFIG_IOT_PROTOCOL_XIAOZHI
        auto& thing_manager = iot::ThingManager::GetInstance();
        thing_manager.AddThing(iot::CreateThing("Speaker"));
        thing_manager.AddThing(iot::CreateThing("Lamp"));
#elif CONFIG_IOT_PROTOCOL_MCP
        static LampController lamp(LAMP_GPIO);
#endif
    }

public:
    CompactMl307Board() : DualNetworkBoard(ML307_TX_PIN, ML307_RX_PIN, 4096),
        boot_button_(BOOT_BUTTON_GPIO),
        touch_button_(TOUCH_BUTTON_GPIO),
        volume_up_button_(VOLUME_UP_BUTTON_GPIO),
        volume_down_button_(VOLUME_DOWN_BUTTON_GPIO) {

        InitializeMl307Power();  // 初始化ML307电源控制 - 控制EN引脚
        InitializeDisplay();
        InitializeButtons();
        InitializeIot();
        InitializeBroadcastFeatures();  // 初始化播报功能
    }

    virtual Led* GetLed() override {
        static SingleLed led(BUILTIN_LED_GPIO);
        return &led;
    }

    virtual AudioCodec* GetAudioCodec() override {
#ifdef AUDIO_I2S_METHOD_SIMPLEX
        static NoAudioCodecSimplex audio_codec(AUDIO_INPUT_SAMPLE_RATE, AUDIO_OUTPUT_SAMPLE_RATE,
            AUDIO_I2S_SPK_GPIO_BCLK, AUDIO_I2S_SPK_GPIO_LRCK, AUDIO_I2S_SPK_GPIO_DOUT, AUDIO_I2S_MIC_GPIO_SCK, AUDIO_I2S_MIC_GPIO_WS, AUDIO_I2S_MIC_GPIO_DIN);
#else
        static NoAudioCodecDuplex audio_codec(AUDIO_INPUT_SAMPLE_RATE, AUDIO_OUTPUT_SAMPLE_RATE,
            AUDIO_I2S_GPIO_BCLK, AUDIO_I2S_GPIO_WS, AUDIO_I2S_GPIO_DOUT, AUDIO_I2S_GPIO_DIN);
#endif
        return &audio_codec;
    }

    virtual Display* GetDisplay() override {
        return display_;
    }
};

DECLARE_BOARD(CompactMl307Board);
