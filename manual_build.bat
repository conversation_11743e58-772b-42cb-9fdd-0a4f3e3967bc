@echo off
setlocal EnableDelayedExpansion

echo ================================================
echo Manual ESP-IDF Build Script
echo ================================================

REM 设置ESP-IDF环境变量
set "IDF_PATH=D:\Espressif\frameworks\esp-idf-v5.4.1"
set "IDF_TOOLS_PATH=D:\Espressif\tools"
set "ESP_IDF_VERSION=5.4.1"

REM 设置Python路径
set "PYTHON=D:\Espressif\python_env\idf5.4_py3.11_env\Scripts\python.exe"

REM 设置工具路径
set "PATH=D:\Espressif\tools\idf-git\2.44.0\cmd;%PATH%"
set "PATH=D:\Espressif\python_env\idf5.4_py3.11_env\Scripts;%PATH%"
set "PATH=D:\Espressif\tools\ninja\1.11.1;%PATH%"
set "PATH=D:\Espressif\tools\cmake\3.24.0\bin;%PATH%"

echo Setting ESP-IDF environment...
echo IDF_PATH: %IDF_PATH%
echo ESP_IDF_VERSION: %ESP_IDF_VERSION%
echo PYTHON: %PYTHON%

REM 切换到项目目录
cd /d "J:\xiaozhi-esp32"

echo Starting build...
%PYTHON% %IDF_PATH%\tools\idf.py build

if %ERRORLEVEL% neq 0 (
    echo Build failed with error code: %ERRORLEVEL%
    pause
    exit /b %ERRORLEVEL%
)

echo Build successful!
pause
