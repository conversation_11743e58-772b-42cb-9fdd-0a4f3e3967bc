## IDF Component Manager Manifest File
dependencies:
  # waveshare/esp_lcd_sh8601: 1.0.2  # Disabled - no display needed
  # espressif/esp_lcd_ili9341: ==1.2.0  # Disabled - no display needed
  # espressif/esp_lcd_gc9a01: ==2.0.1  # Disabled - no display needed
  # espressif/esp_lcd_st77916: ^1.0.1  # Disabled - no display needed
  # espressif/esp_lcd_st7796:  # Disabled - no display needed
  #   version: 1.3.2
  #   rules:
  #   - if: target not in [esp32c3]
  # espressif/esp_lcd_spd2010: ==1.0.2  # Disabled - no display needed
  espressif/esp_io_expander_tca9554: ==2.0.0
  # espressif/esp_lcd_panel_io_additions: ^1.0.1  # Disabled - no display needed
  # 78/esp_lcd_nv3023: ~1.0.0  # Disabled - no display needed
  78/esp-wifi-connect: ~2.4.2
  78/esp-opus-encoder: ~2.3.3
  78/esp-ml307: ~2.2.1
  # 78/xiaozhi-fonts: ~1.3.2  # Disabled - depends on LVGL
  espressif/led_strip: ^2.5.5
  espressif/esp_codec_dev: ~1.3.2
  espressif/esp-sr: ~2.1.1
  espressif/button: ~4.1.3
  espressif/knob: ^1.0.0
  espressif/esp32-camera: ^2.0.15
  # espressif/esp_lcd_touch_ft5x06: ~1.0.7  # Disabled - no display needed
  # espressif/esp_lcd_touch_gt911: ^1  # Disabled - no display needed
  # waveshare/esp_lcd_touch_cst9217: ^1.0.3  # Disabled - no display needed
  # lvgl/lvgl: ~9.2.2  # Disabled - no display needed
  # esp_lvgl_port: ~2.6.0  # Disabled - no display needed
  espressif/esp_io_expander_tca95xx_16bit: ^2.0.0
  # espressif2022/image_player: ^1.1.0  # Disabled - may depend on display components
  espressif/adc_mic: ^0.2.0
  espressif/esp_mmap_assets: '>=1.2'
  # txp666/otto-emoji-gif-component: ~1.0.2  # Disabled - depends on LVGL

  # SenseCAP Watcher Board
  wvirgil123/esp_jpeg_simd:
    version: 1.0.0
    rules:
    - if: target in [esp32s3]
  wvirgil123/sscma_client:
    version: 1.0.2
    rules:
    - if: target in [esp32s3]

  # tny-robotics/sh1106-esp-idf:  # Disabled - no display needed
  #   version: ^1.0.0
  #   rules:
  #   - if: idf_version >= "5.4.0"

  # waveshare/esp_lcd_jd9365_10_1:  # Disabled - no display needed
  #   version: '*'
  #   rules:
  #   - if: target in [esp32p4]
  # waveshare/esp_lcd_st7703:  # Disabled - no display needed
  #   version: '*'
  #   rules:
  #   - if: target in [esp32p4]
  # espressif/esp_lcd_ili9881c:  # Disabled - no display needed
  #   version: ^1.0.1
  #   rules:
  #   - if: target in [esp32p4]
  espressif/esp_wifi_remote:
    version: '*'
    rules:
    - if: target in [esp32p4]
  lijunru-hub/servo_dog_ctrl:
    version: ^0.1.6
    rules:
    - if: target in [esp32c3]

  ## Required IDF version
  idf:
    version: '>=5.4.0'
