#ifndef NETWORK_CONSOLE_H
#define NETWORK_CONSOLE_H

#include <esp_console.h>
#include <esp_log.h>

class NetworkConsole {
public:
    static NetworkConsole& GetInstance();
    
    void Initialize();
    void RegisterNetworkCommands();
    
private:
    NetworkConsole() = default;
    ~NetworkConsole() = default;
    
    // 禁止拷贝和赋值
    NetworkConsole(const NetworkConsole&) = delete;
    NetworkConsole& operator=(const NetworkConsole&) = delete;
    
    // 命令处理函数
    static int CmdSwitchTo4G(int argc, char** argv);
    static int CmdSwitchToWiFi(int argc, char** argv);
    static int CmdNetworkStatus(int argc, char** argv);
    static int CmdHelp(int argc, char** argv);
    static int CmdReboot(int argc, char** argv);
    
    bool initialized_ = false;
};

#endif // NETWORK_CONSOLE_H
