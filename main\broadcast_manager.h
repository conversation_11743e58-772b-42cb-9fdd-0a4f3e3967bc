#ifndef BROADCAST_MANAGER_H
#define BROADCAST_MANAGER_H

#include <string>
#include <functional>
#include <memory>
#include <esp_timer.h>

class Protocol;

/**
 * 统一音频播放管理器
 * 支持三种音频播放方式：
 * 1. 智能交互播报（需要大模型处理）
 * 2. 直接TTS播报（不需要大模型）
 * 3. 预存音频播放（本地音频文件）
 */
class BroadcastManager {
public:
    enum AudioPlayMode {
        SMART_INTERACTION,   // 智能交互模式（需要大模型）
        DIRECT_TTS,          // 直接TTS模式（不需要大模型）
        PRELOADED_AUDIO      // 预存音频模式（本地播放）
    };

    enum BroadcastType {
        WEATHER_REPORT,      // 天气播报
        HOURLY_TIME,         // 整点报时
        DRIVING_TIPS,        // 行车技巧
        SYSTEM_ALERT,        // 系统提醒
        CUSTOM_TEXT          // 自定义文本
    };

    static BroadcastManager& GetInstance();
    
    /**
     * 初始化播报管理器
     * @param protocol 通信协议实例
     */
    void Initialize(Protocol* protocol);
    
    /**
     * 智能交互播报（需要大模型处理）
     * @param query 查询文本，如"今天天气怎么样"
     * @param type 播报类型
     * @param priority 优先级 (0-10, 数字越大优先级越高)
     */
    void PlaySmartInteraction(const std::string& query, BroadcastType type = CUSTOM_TEXT, int priority = 8);

    /**
     * 直接TTS播报（不需要大模型）
     * @param text 要播报的文本内容
     * @param type 播报类型
     * @param priority 优先级 (0-10, 数字越大优先级越高)
     */
    void PlayDirectTTS(const std::string& text, BroadcastType type = CUSTOM_TEXT, int priority = 6);

    /**
     * 预存音频播放（本地音频文件）
     * @param audio_file 音频文件路径或标识
     * @param type 播报类型
     * @param priority 优先级 (0-10, 数字越大优先级越高)
     */
    void PlayPreloadedAudio(const std::string& audio_file, BroadcastType type = SYSTEM_ALERT, int priority = 9);

    /**
     * 统一播放接口（兼容旧版本）
     * @param text 要播报的文本内容
     * @param type 播报类型
     * @param priority 优先级
     */
    void BroadcastText(const std::string& text, BroadcastType type = CUSTOM_TEXT, int priority = 5);
    
    /**
     * 启动定时天气播报
     * @param interval_hours 播报间隔（小时）
     */
    void StartWeatherBroadcast(int interval_hours = 12);
    
    /**
     * 启动整点报时
     */
    void StartHourlyTimeBroadcast();
    
    /**
     * 启动行车技巧播报
     * @param interval_minutes 播报间隔（分钟）
     */
    void StartDrivingTipsBroadcast(int interval_minutes = 30);
    
    /**
     * 停止所有定时播报
     */
    void StopAllBroadcasts();
    
    /**
     * 设置播报状态回调
     */
    void OnBroadcastStart(std::function<void(const std::string& content, BroadcastType type, AudioPlayMode mode)> callback);
    void OnBroadcastEnd(std::function<void(BroadcastType type, AudioPlayMode mode)> callback);

    /**
     * 预加载音频文件到内存
     * @param audio_id 音频标识
     * @param file_path 音频文件路径
     */
    bool PreloadAudioFile(const std::string& audio_id, const std::string& file_path);

    /**
     * 获取预加载音频列表
     */
    std::vector<std::string> GetPreloadedAudioList() const;

private:
    BroadcastManager() = default;
    ~BroadcastManager();
    
    Protocol* protocol_ = nullptr;
    
    // 定时器句柄
    esp_timer_handle_t weather_timer_ = nullptr;
    esp_timer_handle_t hourly_timer_ = nullptr;
    esp_timer_handle_t driving_tips_timer_ = nullptr;
    
    // 回调函数
    std::function<void(const std::string&, BroadcastType)> on_broadcast_start_;
    std::function<void(BroadcastType)> on_broadcast_end_;
    
    // 内部方法
    void SendDirectTTSRequest(const std::string& text, BroadcastType type);
    void CreateWeatherTimer(int interval_hours);
    void CreateHourlyTimer();
    void CreateDrivingTipsTimer(int interval_minutes);
    
    // 定时器回调
    static void WeatherTimerCallback(void* arg);
    static void HourlyTimerCallback(void* arg);
    static void DrivingTipsTimerCallback(void* arg);
    
    // 获取当前时间字符串
    std::string GetCurrentTimeString();
    
    // 获取随机行车技巧
    std::string GetRandomDrivingTip();
};

#endif // BROADCAST_MANAGER_H
