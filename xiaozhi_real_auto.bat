@echo off
setlocal EnableDelayedExpansion
title Xiaozhi ESP32 AI Automation System

REM 设置ESP-IDF环境变量
set "IDF_PATH=D:\Espressif\frameworks\esp-idf-v5.4.1"
set "IDF_TOOLS_PATH=D:\Espressif\tools"
set "ESP_IDF_VERSION=5.4.1"
echo Setting ESP-IDF environment...
echo IDF_PATH: %IDF_PATH%
echo ESP_IDF_VERSION: %ESP_IDF_VERSION%

REM 设置临时环境变量来绕过CMake问题
set "CMAKE_BUILD_PARALLEL_LEVEL=1"
set "NINJA_STATUS=[%%f/%%t] "

REM 初始化ESP-IDF环境
call "%IDF_PATH%\export.bat" >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Warning: ESP-IDF environment setup may have issues
)

echo ========================================
echo   Xiaozhi ESP32 AI Automation System
echo ========================================
echo.
echo AI-Driven Smart Build and Monitor System
echo Features:
echo    1. AI Smart Code Analysis
echo    2. Auto Build Mode Selection
echo    3. Auto Monitor System
echo    4. Smart Error Detection
echo    5. Full Automation Process
echo.

REM Change to project directory
cd /d "J:\xiaozhi-esp32"
echo Working Directory: %CD%
echo.

echo ================================================
echo Step 1: Smart Build Mode Selection
echo ================================================
echo Analyzing build requirements...

REM Simple logic: if build directory doesn't exist, use CLEAN
if not exist "build" (
    set BUILD_MODE=CLEAN
    set BUILD_REASON=First time build or build directory missing
    echo Decision: Clean build required
) else (
    set BUILD_MODE=QUICK
    set BUILD_REASON=Build directory exists, using quick build
    echo Decision: Quick build selected
)

echo.
echo Build Decision Complete:
echo    Build Mode: %BUILD_MODE%
echo    Reason: %BUILD_REASON%
echo    Analysis Time: %time%
echo.

echo ================================================
echo Step 2: Auto Start Monitor System
echo ================================================
echo Monitor system will start after build completion
echo.

echo ================================================
echo Step 3: Execute Smart Build Flash
echo ================================================
echo Executing build based on AI decision...

if "%BUILD_MODE%"=="CLEAN" (
    echo Executing clean rebuild mode...
    echo Cleaning build directory...
    rmdir /s /q build 2>nul
    echo Building project...
    idf.py build
    if !ERRORLEVEL! EQU 0 (
        echo Flashing to device...
        idf.py flash
        if !ERRORLEVEL! EQU 0 (
            echo Starting monitor...
            idf.py monitor
        )
    )
) else (
    echo Executing quick build mode...
    echo Building project...
    idf.py build
    if !ERRORLEVEL! EQU 0 (
        echo Flashing to device...
        idf.py flash
        if !ERRORLEVEL! EQU 0 (
            echo Starting monitor...
            idf.py monitor
        )
    )
)

REM Check execution result
echo.
echo ================================================
echo Step 4: Execution Result Analysis
echo ================================================
if %ERRORLEVEL% EQU 0 (
    echo SUCCESS: Automation process completed
    echo Build Mode: %BUILD_MODE%
    echo Completion Time: %time%
    echo.
    echo System should be running, monitor these status:
    echo    - WiFi connection success
    echo    - Network console started
    echo    - Voice wake-up ready
    echo    - TTS system initialized
    echo    - BLE GATT server started
    echo    - System startup complete: "Hello XiaoZhi"
    echo.
    echo Available test commands:
    echo    - help     : Show help information
    echo    - tts time : Test time announcement
    echo    - status   : Check system status
    echo    - 4g       : Check 4G status
    echo    - wifi     : Check WiFi status
    echo.
    echo AI_READY: System ready, auto monitor continues
) else (
    echo FAILED: Automation process failed
    echo Build Mode: %BUILD_MODE%
    echo Error Code: %ERRORLEVEL%
    echo Failure Time: %time%
    echo.
    echo AI_ACTION: Check auto monitor logs for analysis
)

echo.
echo ================================================
echo Xiaozhi AI Automation System Complete
echo ================================================
echo Overall Status: Process completed
echo Monitor Status: Auto monitor continues running
echo Log Location: monitor_log_*.txt
echo End Time: %time%
echo.
echo TIP: Auto monitor will continue running to monitor ESP32 startup
REM pause - Automation script does not need pause
