#ifndef PRELOADED_AUDIO_MANAGER_H
#define PRELOADED_AUDIO_MANAGER_H

#include <string>
#include <map>
#include <vector>
#include <functional>
#include <cstdint>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>

class AudioCodec;

/**
 * 预存音频管理器
 * 负责管理和播放存储在Flash中的音频文件
 * 支持流式播放，最小化内存占用
 */
class PreloadedAudioManager {
public:
    enum AudioCategory {
        SYSTEM_SOUNDS,      // 系统音效
        DRIVING_ALERTS,     // 行车提醒
        TIME_CHIMES,        // 时间提醒
        EMERGENCY_ALERTS    // 紧急警告
    };
    
    struct AudioResource {
        const char* id;                 // 音频标识
        const uint8_t* data;           // 音频数据指针
        size_t size;                   // 数据大小
        AudioCategory category;         // 音频分类
        int priority;                  // 默认优先级
        const char* description;       // 描述信息
    };
    
    struct PlaybackStatus {
        bool is_playing;
        std::string current_audio_id;
        size_t bytes_played;
        size_t total_bytes;
        uint32_t duration_ms;
        uint32_t position_ms;
    };

    static PreloadedAudioManager& GetInstance();
    
    /**
     * 初始化预存音频管理器
     */
    void Initialize();
    
    /**
     * 播放预存音频
     * @param audio_id 音频标识
     * @param callback 播放完成回调
     * @return 是否成功开始播放
     */
    bool PlayAudio(const std::string& audio_id, 
                   std::function<void(bool success)> callback = nullptr);
    
    /**
     * 停止当前播放
     */
    void StopPlayback();
    
    /**
     * 暂停/恢复播放
     */
    void PausePlayback();
    void ResumePlayback();
    
    /**
     * 查询功能
     */
    bool IsAudioAvailable(const std::string& audio_id) const;
    std::vector<std::string> GetAvailableAudioList() const;
    std::vector<std::string> GetAudioListByCategory(AudioCategory category) const;
    PlaybackStatus GetPlaybackStatus() const;
    
    /**
     * 音频信息查询
     */
    const AudioResource* GetAudioResource(const std::string& audio_id) const;
    size_t GetTotalAudioSize() const;
    size_t GetCategorySize(AudioCategory category) const;
    
    /**
     * 回调设置
     */
    void OnPlaybackStarted(std::function<void(const std::string& audio_id)> callback);
    void OnPlaybackCompleted(std::function<void(const std::string& audio_id, bool success)> callback);
    void OnPlaybackProgress(std::function<void(const std::string& audio_id, uint32_t position_ms, uint32_t duration_ms)> callback);

private:
    PreloadedAudioManager() = default;
    ~PreloadedAudioManager();
    
    // 音频资源索引
    std::map<std::string, const AudioResource*> audio_index_;
    static const AudioResource audio_resources_[];
    
    // 播放状态
    bool is_playing_ = false;
    bool is_paused_ = false;
    std::string current_audio_id_;
    const AudioResource* current_resource_ = nullptr;
    size_t playback_position_ = 0;
    
    // 流式播放缓冲区
    static constexpr size_t STREAM_BUFFER_SIZE = 4096;  // 4KB缓冲区
    uint8_t stream_buffer_[STREAM_BUFFER_SIZE];
    
    // 回调函数
    std::function<void(const std::string&)> on_playback_started_;
    std::function<void(const std::string&, bool)> on_playback_completed_;
    std::function<void(const std::string&, uint32_t, uint32_t)> on_playback_progress_;
    std::function<void(bool)> current_completion_callback_;
    
    // 核心方法
    void InitializeAudioIndex();
    bool StartStreamingPlayback(const AudioResource* resource);
    void StreamingPlaybackTask();
    bool PlayAudioChunk(const uint8_t* data, size_t size);
    void OnPlaybackFinished(bool success);
    
    // 音频格式处理
    bool IsValidP3Format(const uint8_t* data, size_t size) const;
    size_t GetP3HeaderSize(const uint8_t* data) const;
    const uint8_t* GetP3AudioData(const uint8_t* data) const;
    size_t GetP3AudioSize(const uint8_t* data, size_t total_size) const;
    
    // 任务管理
    TaskHandle_t streaming_task_handle_ = nullptr;
    static void StreamingTaskWrapper(void* parameter);
    
    // 工具方法
    AudioCodec* GetAudioCodec() const;
    uint32_t CalculateAudioDuration(const AudioResource* resource) const;
};

// 预定义的音频资源ID常量
namespace AudioIds {
    // 系统音效
    extern const char* STARTUP;
    extern const char* CONNECTED;
    extern const char* DISCONNECTED;
    extern const char* LOW_BATTERY;
    extern const char* ERROR;
    extern const char* SUCCESS;
    extern const char* EMERGENCY;
    extern const char* BUTTON_CLICK;
    
    // 行车提醒
    extern const char* NIGHT_DRIVING;
    extern const char* SPEED_WARNING;
    extern const char* REST_REMINDER;
    extern const char* WEATHER_ALERT;
    
    // 时间提醒
    extern const char* HOUR_CHIME;
    extern const char* HALF_HOUR;
    extern const char* QUARTER_HOUR;
    extern const char* ALARM;
}

#endif // PRELOADED_AUDIO_MANAGER_H
