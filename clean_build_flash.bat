@echo off
setlocal EnableDelayedExpansion
title ESP32-S3 Clean Rebuild

echo ================================================
echo ESP32-S3 Clean Rebuild Mode
echo ================================================
echo Features: Delete all cache, complete recompilation
echo Estimated time: 5-10 minutes
echo Start time: %time%
echo ================================================
echo.

cd /d "J:\xiaozhi-esp32-main"
echo Working directory: %CD%

REM Cleanup environment
call :cleanup_build

REM Environment setup
call :setup_environment

REM Clean compile
call :clean_compile

REM Check compilation result
if %ERRORLEVEL% NEQ 0 (
    echo Clean rebuild failed, please check code errors
    goto :end
)

REM Flash firmware
call :flash_firmware

REM Check flash result
if %ERRORLEVEL% NEQ 0 (
    echo Flash failed, please check COM13 port connection
    goto :end
)

REM Start monitoring
call :start_monitoring

goto :end

:cleanup_build
echo.
echo ================================================
echo Cleanup Phase
echo ================================================
echo AI_MONITOR_PHASE: CLEANUP_STARTING
echo AI_MONITOR_TIME: %time%

REM Kill processes that might be in use
taskkill /f /im python.exe 2>nul
taskkill /f /im ninja.exe 2>nul
taskkill /f /im cmake.exe 2>nul
timeout /t 2 /nobreak >nul

if exist "build" (
    echo Deleting build directory...
    rmdir /s /q "build"
    echo Build directory deleted
)
if exist ".cache" (
    echo Deleting .cache directory...
    rmdir /s /q ".cache"
    echo .cache directory deleted
)
if exist "CMakeCache.txt" (
    del "CMakeCache.txt"
    echo CMakeCache.txt deleted
)
if exist "cmake_install.cmake" (
    del "cmake_install.cmake"
    echo cmake_install.cmake deleted
)

echo AI_MONITOR_RESULT: CLEANUP_COMPLETE
echo Cleanup complete
return

:setup_environment
echo.
echo ================================================
echo Environment Setup Phase
echo ================================================
echo AI_MONITOR_PHASE: ENVIRONMENT_SETUP
echo AI_MONITOR_TIME: %time%

set IDF_PATH=D:\Espressif\frameworks\esp-idf-v5.4.1
set IDF_TOOLS_PATH=D:\Espressif
set IDF_PYTHON_ENV_PATH=D:\Espressif\python_env\idf5.4_py3.11_env
set PYTHON=D:\Espressif\python_env\idf5.4_py3.11_env\Scripts\python.exe

set PATH=D:\Espressif\tools\xtensa-esp-elf\esp-14.2.0_20241119\xtensa-esp-elf\bin;%PATH%
set PATH=D:\Espressif\tools\cmake\3.30.2\bin;%PATH%
set PATH=D:\Espressif\tools\ninja\1.12.1;%PATH%
set PATH=D:\Espressif\python_env\idf5.4_py3.11_env\Scripts;%PATH%
set PATH=D:\Espressif\tools\idf-git\2.44.0\cmd;%PATH%

echo ESP-IDF v5.4.1 environment configured
echo AI_MONITOR_RESULT: ENVIRONMENT_READY
return

:clean_compile
echo.
echo ================================================
echo PHASE_START: CLEAN_COMPILATION
echo ================================================
echo AI_MONITOR_PHASE: CLEAN_BUILD_STARTING
echo AI_MONITOR_TIME: %time%
echo AI_MONITOR_EXPECT: Complete recompilation, takes longer time
echo AI_MONITOR_DURATION: Estimated 5-10 minutes
echo ================================================
echo.

echo Starting complete recompilation...
echo Step 1: Execute fullclean...
%PYTHON% %IDF_PATH%\tools\idf.py fullclean

echo Step 2: Start complete compilation...
%PYTHON% %IDF_PATH%\tools\idf.py build

echo.
echo ================================================
echo PHASE_END: CLEAN_COMPILATION
echo ================================================
if %ERRORLEVEL% EQU 0 (
    echo AI_MONITOR_RESULT: CLEAN_BUILD_SUCCESS
    echo AI_MONITOR_NEXT: FLASH_PHASE_STARTING
    echo Clean rebuild successful
) else (
    echo AI_MONITOR_RESULT: CLEAN_BUILD_FAILED
    echo AI_MONITOR_ERROR_CODE: %ERRORLEVEL%
    echo AI_MONITOR_ACTION: CHECK_CODE_ERRORS
    echo Clean rebuild failed, error code: %ERRORLEVEL%
)
echo AI_MONITOR_TIME: %time%
echo ================================================
return

:flash_firmware
echo.
echo ================================================
echo PHASE_START: FLASHING
echo ================================================
echo AI_MONITOR_PHASE: FLASH_STARTING
echo AI_MONITOR_TIME: %time%
echo AI_MONITOR_EXPECT: Flash progress and verification info
echo AI_MONITOR_DURATION: Estimated 30 seconds to 2 minutes
echo AI_MONITOR_TARGET: COM13 port
echo ================================================
echo.

echo Starting flash to COM13...
%PYTHON% %IDF_PATH%\tools\idf.py -p COM13 flash

echo.
echo ================================================
echo PHASE_END: FLASHING
echo ================================================
if %ERRORLEVEL% EQU 0 (
    echo AI_MONITOR_RESULT: FLASH_SUCCESS
    echo AI_MONITOR_NEXT: MONITOR_PHASE_STARTING
    echo Flash successful
) else (
    echo AI_MONITOR_RESULT: FLASH_FAILED
    echo AI_MONITOR_ERROR_CODE: %ERRORLEVEL%
    echo AI_MONITOR_ACTION: CHECK_COM_PORT_AND_CONNECTION
    echo Flash failed, error code: %ERRORLEVEL%
)
echo AI_MONITOR_TIME: %time%
echo ================================================
return

:start_monitoring
echo.
echo ================================================
echo PHASE_START: ESP32_MONITORING
echo ================================================
echo AI_MONITOR_PHASE: ESP32_RUNTIME_STARTING
echo AI_MONITOR_TIME: %time%
echo AI_MONITOR_EXPECT: System startup logs and "Hello XiaoZhi"
echo AI_MONITOR_DURATION: At least 2 minutes
echo AI_MONITOR_SUCCESS_SIGNAL: "Hello XiaoZhi"
echo ================================================
echo.
echo Key monitoring points:
echo    WiFi connection success
echo    Network console startup
echo    TTS system initialization
echo    Voice wake-up ready
echo    BLE GATT server startup
echo    System startup complete: "Hello XiaoZhi"
echo.
echo AI_MONITOR_START: ESP32_LOGS_BEGIN
echo Starting ESP32 monitoring...

%PYTHON% %IDF_PATH%\tools\idf.py -p COM13 monitor

echo AI_MONITOR_END: ESP32_LOGS_END
return

:end
echo.
echo ================================================
echo Clean Rebuild Process Complete
echo ================================================
echo End time: %time%
echo AI_MONITOR_PHASE: CLEAN_BUILD_COMPLETE
