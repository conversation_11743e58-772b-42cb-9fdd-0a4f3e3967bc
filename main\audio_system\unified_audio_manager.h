#ifndef UNIFIED_AUDIO_MANAGER_H
#define UNIFIED_AUDIO_MANAGER_H

#include <string>
#include <memory>
#include <functional>
#include <queue>
#include <mutex>

class Protocol;
class PreloadedAudioManager;
class DirectTTSHandler;
class SmartBroadcastHandler;

/**
 * 统一音频播放管理器
 * 负责协调三种音频播放方式：
 * 1. 智能交互播报（需要大模型）
 * 2. 直接TTS播报（不需要大模型）
 * 3. 预存音频播放（本地音频文件）
 */
class UnifiedAudioManager {
public:
    enum PlayMode {
        AUTO_SELECT,        // 自动选择最优方式
        FORCE_SMART,        // 强制智能交互
        FORCE_DIRECT_TTS,   // 强制直接TTS
        FORCE_PRELOADED     // 强制预存音频
    };
    
    enum AudioScenario {
        WEATHER_QUERY,      // 天气查询 → 智能交互
        NEWS_BRIEF,         // 新闻播报 → 智能交互
        DRIVING_SAFETY,     // 行车安全 → 直接TTS
        TIME_ANNOUNCEMENT,  // 时间播报 → 直接TTS
        SYSTEM_ALERT,       // 系统提醒 → 预存音频
        EMERGENCY_WARNING,  // 紧急警告 → 预存音频
        CUSTOM_TEXT         // 自定义文本 → 自动选择
    };
    
    enum PlaybackState {
        IDLE,
        PREPARING,
        PLAYING,
        PAUSED,
        ERROR
    };

    struct AudioRequest {
        std::string content;
        AudioScenario scenario;
        PlayMode mode;
        int priority;
        uint64_t timestamp;
        std::function<void(bool success)> completion_callback;
        
        // 优先级比较器（优先级高的先播放）
        bool operator<(const AudioRequest& other) const {
            return priority < other.priority;
        }
    };

    static UnifiedAudioManager& GetInstance();
    
    /**
     * 初始化音频管理器
     * @param protocol 通信协议实例
     */
    void Initialize(Protocol* protocol);
    
    /**
     * 统一播放接口
     * @param content 内容（文本或音频ID）
     * @param scenario 播放场景
     * @param mode 播放模式
     * @param priority 优先级（0-10，数字越大优先级越高）
     * @param callback 完成回调
     */
    void PlayAudio(const std::string& content, 
                   AudioScenario scenario = CUSTOM_TEXT,
                   PlayMode mode = AUTO_SELECT,
                   int priority = 5,
                   std::function<void(bool success)> callback = nullptr);
    
    /**
     * 场景化播放接口
     */
    void PlayWeatherQuery(const std::string& query);
    void PlayDrivingSafety(const std::string& tip);
    void PlaySystemAlert(const std::string& alert_id);
    void PlayTimeAnnouncement();
    void PlayEmergencyWarning(const std::string& warning_id);
    
    /**
     * 播放控制
     */
    void StopCurrentPlayback();
    void PausePlayback();
    void ResumePlayback();
    void ClearQueue();
    
    /**
     * 状态查询
     */
    PlaybackState GetPlaybackState() const { return current_state_; }
    size_t GetQueueSize() const;
    bool IsPlaying() const { return current_state_ == PLAYING; }
    
    /**
     * 回调设置
     */
    void OnPlaybackStateChanged(std::function<void(PlaybackState state)> callback);
    void OnPlaybackStarted(std::function<void(const std::string& content, AudioScenario scenario)> callback);
    void OnPlaybackCompleted(std::function<void(bool success)> callback);

private:
    UnifiedAudioManager() = default;
    ~UnifiedAudioManager();
    
    // 核心组件
    Protocol* protocol_ = nullptr;
    PreloadedAudioManager* preloaded_manager_ = nullptr;
    std::unique_ptr<DirectTTSHandler> direct_tts_handler_;
    std::unique_ptr<SmartBroadcastHandler> smart_handler_;
    
    // 播放队列和状态
    std::priority_queue<AudioRequest> request_queue_;
    mutable std::mutex queue_mutex_;
    PlaybackState current_state_ = IDLE;
    AudioRequest current_request_;
    
    // 回调函数
    std::function<void(PlaybackState)> on_state_changed_;
    std::function<void(const std::string&, AudioScenario)> on_playback_started_;
    std::function<void(bool)> on_playback_completed_;
    
    // 核心方法
    PlayMode SelectOptimalMode(const std::string& content, AudioScenario scenario);
    void ProcessNextRequest();
    void ProcessAudioRequest(const AudioRequest& request);
    void SetPlaybackState(PlaybackState state);
    void OnRequestCompleted(bool success);
    
    // 系统状态检查
    bool IsNetworkAvailable() const;
    bool IsMemorySufficient(size_t required_memory) const;
    size_t GetAvailableMemory() const;
};

#endif // UNIFIED_AUDIO_MANAGER_H
