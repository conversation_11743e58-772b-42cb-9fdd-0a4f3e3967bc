#include "broadcast_manager.h"
#include "protocols/protocol.h"
#include <esp_log.h>
#include <cJSON.h>
#include <time.h>
#include <random>
#include <vector>

#define TAG "BroadcastManager"

BroadcastManager& BroadcastManager::GetInstance() {
    static BroadcastManager instance;
    return instance;
}

BroadcastManager::~BroadcastManager() {
    StopAllBroadcasts();
}

void BroadcastManager::Initialize(Protocol* protocol) {
    protocol_ = protocol;
    ESP_LOGI(TAG, "BroadcastManager initialized");
}

void BroadcastManager::BroadcastText(const std::string& text, BroadcastType type, int priority) {
    if (!protocol_ || !protocol_->IsAudioChannelOpened()) {
        ESP_LOGW(TAG, "Protocol not available, cannot broadcast: %s", text.c_str());
        return;
    }
    
    ESP_LOGI(TAG, "Broadcasting text (type=%d, priority=%d): %s", type, priority, text.c_str());
    
    // 触发播报开始回调
    if (on_broadcast_start_) {
        on_broadcast_start_(text, type);
    }
    
    // 发送直接TTS请求
    SendDirectTTSRequest(text, type);
}

void BroadcastManager::SendDirectTTSRequest(const std::string& text, BroadcastType type) {
    // 构造MCP消息，请求服务器进行直接TTS转换
    cJSON* payload = cJSON_CreateObject();
    cJSON* jsonrpc = cJSON_CreateString("2.0");
    cJSON* method = cJSON_CreateString("tools/call");
    cJSON* id = cJSON_CreateNumber(1);
    
    cJSON* params = cJSON_CreateObject();
    cJSON* name = cJSON_CreateString("direct_tts");
    cJSON* arguments = cJSON_CreateObject();
    cJSON* text_arg = cJSON_CreateString(text.c_str());
    cJSON* type_arg = cJSON_CreateNumber(type);
    
    cJSON_AddItemToObject(arguments, "text", text_arg);
    cJSON_AddItemToObject(arguments, "type", type_arg);
    cJSON_AddItemToObject(params, "name", name);
    cJSON_AddItemToObject(params, "arguments", arguments);
    
    cJSON_AddItemToObject(payload, "jsonrpc", jsonrpc);
    cJSON_AddItemToObject(payload, "method", method);
    cJSON_AddItemToObject(payload, "params", params);
    cJSON_AddItemToObject(payload, "id", id);
    
    char* payload_str = cJSON_Print(payload);
    if (payload_str) {
        protocol_->SendMcpMessage(std::string(payload_str));
        free(payload_str);
    }
    
    cJSON_Delete(payload);
}

void BroadcastManager::StartWeatherBroadcast(int interval_hours) {
    ESP_LOGI(TAG, "Starting weather broadcast every %d hours", interval_hours);
    CreateWeatherTimer(interval_hours);
}

void BroadcastManager::StartHourlyTimeBroadcast() {
    ESP_LOGI(TAG, "Starting hourly time broadcast");
    CreateHourlyTimer();
}

void BroadcastManager::StartDrivingTipsBroadcast(int interval_minutes) {
    ESP_LOGI(TAG, "Starting driving tips broadcast every %d minutes", interval_minutes);
    CreateDrivingTipsTimer(interval_minutes);
}

void BroadcastManager::StopAllBroadcasts() {
    if (weather_timer_) {
        esp_timer_stop(weather_timer_);
        esp_timer_delete(weather_timer_);
        weather_timer_ = nullptr;
    }
    
    if (hourly_timer_) {
        esp_timer_stop(hourly_timer_);
        esp_timer_delete(hourly_timer_);
        hourly_timer_ = nullptr;
    }
    
    if (driving_tips_timer_) {
        esp_timer_stop(driving_tips_timer_);
        esp_timer_delete(driving_tips_timer_);
        driving_tips_timer_ = nullptr;
    }
    
    ESP_LOGI(TAG, "All broadcasts stopped");
}

void BroadcastManager::CreateWeatherTimer(int interval_hours) {
    if (weather_timer_) {
        esp_timer_stop(weather_timer_);
        esp_timer_delete(weather_timer_);
    }
    
    esp_timer_create_args_t timer_args = {
        .callback = WeatherTimerCallback,
        .arg = this,
        .dispatch_method = ESP_TIMER_TASK,
        .name = "weather_broadcast_timer",
        .skip_unhandled_events = true
    };
    
    esp_timer_create(&timer_args, &weather_timer_);
    esp_timer_start_periodic(weather_timer_, interval_hours * 60 * 60 * 1000000ULL); // 转换为微秒
}

void BroadcastManager::CreateHourlyTimer() {
    if (hourly_timer_) {
        esp_timer_stop(hourly_timer_);
        esp_timer_delete(hourly_timer_);
    }
    
    esp_timer_create_args_t timer_args = {
        .callback = HourlyTimerCallback,
        .arg = this,
        .dispatch_method = ESP_TIMER_TASK,
        .name = "hourly_broadcast_timer",
        .skip_unhandled_events = true
    };
    
    esp_timer_create(&timer_args, &hourly_timer_);
    
    // 计算到下一个整点的时间
    time_t now = time(NULL);
    struct tm* timeinfo = localtime(&now);
    int minutes_to_next_hour = 60 - timeinfo->tm_min;
    int seconds_to_next_hour = minutes_to_next_hour * 60 - timeinfo->tm_sec;
    
    // 先启动一次性定时器到下一个整点，然后启动周期性定时器
    esp_timer_start_once(hourly_timer_, seconds_to_next_hour * 1000000ULL);
}

void BroadcastManager::CreateDrivingTipsTimer(int interval_minutes) {
    if (driving_tips_timer_) {
        esp_timer_stop(driving_tips_timer_);
        esp_timer_delete(driving_tips_timer_);
    }
    
    esp_timer_create_args_t timer_args = {
        .callback = DrivingTipsTimerCallback,
        .arg = this,
        .dispatch_method = ESP_TIMER_TASK,
        .name = "driving_tips_timer",
        .skip_unhandled_events = true
    };
    
    esp_timer_create(&timer_args, &driving_tips_timer_);
    esp_timer_start_periodic(driving_tips_timer_, interval_minutes * 60 * 1000000ULL); // 转换为微秒
}

void BroadcastManager::WeatherTimerCallback(void* arg) {
    auto* manager = static_cast<BroadcastManager*>(arg);
    manager->BroadcastText("今天天气怎么样", WEATHER_REPORT, 8);
}

void BroadcastManager::HourlyTimerCallback(void* arg) {
    auto* manager = static_cast<BroadcastManager*>(arg);
    std::string time_text = "现在是" + manager->GetCurrentTimeString();
    manager->BroadcastText(time_text, HOURLY_TIME, 6);
    
    // 重新启动周期性定时器（每小时）
    if (manager->hourly_timer_) {
        esp_timer_stop(manager->hourly_timer_);
        esp_timer_start_periodic(manager->hourly_timer_, 60 * 60 * 1000000ULL); // 1小时
    }
}

void BroadcastManager::DrivingTipsTimerCallback(void* arg) {
    auto* manager = static_cast<BroadcastManager*>(arg);
    std::string tip = manager->GetRandomDrivingTip();
    manager->BroadcastText(tip, DRIVING_TIPS, 4);
}

std::string BroadcastManager::GetCurrentTimeString() {
    time_t now = time(NULL);
    struct tm* timeinfo = localtime(&now);
    char time_str[64];
    strftime(time_str, sizeof(time_str), "%H点%M分", timeinfo);
    return std::string(time_str);
}

std::string BroadcastManager::GetRandomDrivingTip() {
    static const std::vector<std::string> tips = {
        "行车安全提醒：保持安全车距，避免跟车过近",
        "驾驶小贴士：转弯前提前减速，确保行车安全",
        "安全提醒：雨天路滑，请降低车速谨慎驾驶",
        "行车提醒：定期检查轮胎气压，确保行车安全",
        "驾驶建议：长途驾驶每2小时休息一次，避免疲劳驾驶",
        "安全提示：夜间行车开启大灯，注意观察路况",
        "行车贴士：遇到紧急情况保持冷静，安全第一"
    };
    
    static std::random_device rd;
    static std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, tips.size() - 1);
    
    return tips[dis(gen)];
}

void BroadcastManager::OnBroadcastStart(std::function<void(const std::string&, BroadcastType)> callback) {
    on_broadcast_start_ = callback;
}

void BroadcastManager::OnBroadcastEnd(std::function<void(BroadcastType)> callback) {
    on_broadcast_end_ = callback;
}
