#include "network_console.h"
#include "settings.h"
#include "board.h"
#include "boards/common/dual_network_board.h"
#include "application.h"
#include "broadcast_manager.h"
#include "audio_system/local_audio_manager.h"

#include <esp_console.h>
#include <esp_log.h>
#include <esp_mac.h>
#include <esp_chip_info.h>
#include <esp_ota_ops.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <string.h>

#define TAG "NetworkConsole"

NetworkConsole& NetworkConsole::GetInstance() {
    static NetworkConsole instance;
    return instance;
}

void NetworkConsole::Initialize() {
    if (initialized_) {
        return;
    }

    esp_console_repl_t *repl = NULL;
    esp_console_repl_config_t repl_config = ESP_CONSOLE_REPL_CONFIG_DEFAULT();
    repl_config.max_cmdline_length = 1024;
    repl_config.prompt = "xiaozhi>";

    RegisterNetworkCommands();

    esp_console_dev_uart_config_t hw_config = ESP_CONSOLE_DEV_UART_CONFIG_DEFAULT();
    ESP_ERROR_CHECK(esp_console_new_repl_uart(&hw_config, &repl_config, &repl));
    ESP_ERROR_CHECK(esp_console_start_repl(repl));

    initialized_ = true;
    ESP_LOGI(TAG, "Network console initialized. Type 'nethelp' for available commands.");
}

void NetworkConsole::RegisterNetworkCommands() {
    // 4G命令
    const esp_console_cmd_t cmd_4g = {
        .command = "4g",
        .help = "Switch to 4G network (ML307)",
        .hint = nullptr,
        .func = CmdSwitchTo4G,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_4g));

    // WiFi命令
    const esp_console_cmd_t cmd_wifi = {
        .command = "wifi",
        .help = "Switch to WiFi network",
        .hint = nullptr,
        .func = CmdSwitchToWiFi,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_wifi));

    // 网络状态命令
    const esp_console_cmd_t cmd_status = {
        .command = "status",
        .help = "Show current network status",
        .hint = nullptr,
        .func = CmdNetworkStatus,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_status));

    // 帮助命令
    const esp_console_cmd_t cmd_help_net = {
        .command = "nethelp",
        .help = "Show network commands help",
        .hint = nullptr,
        .func = CmdHelp,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_help_net));

    // 重启命令
    const esp_console_cmd_t cmd_reboot = {
        .command = "reboot",
        .help = "Reboot the device",
        .hint = nullptr,
        .func = CmdReboot,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_reboot));

    // 音频播报测试命令
    const esp_console_cmd_t cmd_broadcast_text = {
        .command = "broadcast",
        .help = "Broadcast custom text via TTS",
        .hint = "<text>",
        .func = CmdBroadcastText,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_broadcast_text));

    const esp_console_cmd_t cmd_broadcast_weather = {
        .command = "weather",
        .help = "Broadcast weather query",
        .hint = nullptr,
        .func = CmdBroadcastWeather,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_broadcast_weather));

    const esp_console_cmd_t cmd_broadcast_time = {
        .command = "time",
        .help = "Broadcast current time",
        .hint = nullptr,
        .func = CmdBroadcastTime,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_broadcast_time));

    const esp_console_cmd_t cmd_play_audio = {
        .command = "play",
        .help = "Play preloaded audio",
        .hint = "<audio_id>",
        .func = CmdPlayAudio,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_play_audio));

    const esp_console_cmd_t cmd_audio_help = {
        .command = "audiohelp",
        .help = "Show audio broadcast commands help",
        .hint = nullptr,
        .func = CmdAudioHelp,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_audio_help));

    // 智能播报命令（需要大模型处理）
    const esp_console_cmd_t cmd_smart_weather = {
        .command = "smart_weather",
        .help = "Smart weather query (requires AI model)",
        .hint = nullptr,
        .func = CmdSmartWeather,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_smart_weather));

    const esp_console_cmd_t cmd_smart_time = {
        .command = "smart_time",
        .help = "Smart time query (requires AI model)",
        .hint = nullptr,
        .func = CmdSmartTime,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_smart_time));

    // 直接TTS命令（不需要大模型）
    const esp_console_cmd_t cmd_direct_tts = {
        .command = "direct_tts",
        .help = "Direct TTS without AI model",
        .hint = "<text>",
        .func = CmdDirectTTS,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_direct_tts));

    // 本地音频播放命令
    const esp_console_cmd_t cmd_play_local = {
        .command = "local",
        .help = "Play local audio file",
        .hint = "<audio_number>",
        .func = CmdPlayLocal,
        .argtable = nullptr
    };
    ESP_ERROR_CHECK(esp_console_cmd_register(&cmd_play_local));
}

int NetworkConsole::CmdSwitchTo4G(int argc, char** argv) {
    printf("Switching to 4G network (ML307)...\n");

    // 检查设备是否支持双网络
    auto& board = Board::GetInstance();
    // 通过板卡类型字符串检查是否支持双网络
    std::string board_type = board.GetBoardType();
    if (board_type != "wifi" && board_type != "ml307") {
        printf("❌ Error: This device does not support dual network mode.\n");
        printf("   4G network is not available on this device.\n");
        printf("   Available commands: wifi, status, nethelp\n");
        return 1;
    }

    // 对于支持双网络的板卡，使用static_cast（我们已经通过类型检查确认了）
    auto* dual_board = static_cast<DualNetworkBoard*>(&board);

    if (dual_board->GetNetworkType() == NetworkType::ML307) {
        printf("✅ Already using 4G network.\n");
        return 0;
    }

    printf("Saving network type to settings...\n");
    Settings settings("network", true);
    settings.SetInt("type", 1); // 1 = ML307 (4G)

    printf("Network type set to 4G. Rebooting...\n");
    vTaskDelay(pdMS_TO_TICKS(1000));
    esp_restart();

    return 0;
}

int NetworkConsole::CmdNetworkStatus(int argc, char** argv) {
    printf("\n=== Network Status ===\n");

    // 获取设备信息
    auto& board = Board::GetInstance();

    // 读取当前网络设置
    Settings settings("network", false);
    int network_type = settings.GetInt("type", 1); // 默认ML307

    // 通过板卡类型检查是否支持双网络
    std::string board_type = board.GetBoardType();
    bool is_dual_network = (board_type == "wifi" || board_type == "ml307");

    if (is_dual_network) {
        auto* dual_board = static_cast<DualNetworkBoard*>(&board);
        printf("Device Type: Dual Network (WiFi + 4G)\n");

        NetworkType current_type = dual_board->GetNetworkType();
        if (current_type == NetworkType::ML307) {
            printf("Current Network: 4G (ML307)\n");
            printf("Network Icon: %s\n", dual_board->GetNetworkStateIcon());
        } else {
            printf("Current Network: WiFi\n");
            printf("Network Icon: %s\n", dual_board->GetNetworkStateIcon());
        }
        printf("Supported Networks: WiFi, 4G (ML307)\n");
        printf("Network Switching: ✅ Available\n");
    } else {
        printf("Device Type: WiFi Only\n");
        printf("Current Network: WiFi (Only)\n");
        printf("Supported Networks: WiFi only\n");
        printf("Network Switching: ❌ Not available\n");
        if (network_type == 1) {
            printf("⚠️  Warning: 4G setting detected but device only supports WiFi\n");
        }
    }

    // 显示设备信息
    printf("\n=== Device Info ===\n");

    // MAC地址
    uint8_t mac[6];
    esp_read_mac(mac, ESP_MAC_WIFI_STA);
    printf("WiFi MAC: " MACSTR "\n", MAC2STR(mac));

    // 芯片信息
    esp_chip_info_t chip_info;
    esp_chip_info(&chip_info);
    printf("Chip: %s Rev %d\n",
           chip_info.model == CHIP_ESP32 ? "ESP32" :
           chip_info.model == CHIP_ESP32S2 ? "ESP32-S2" :
           chip_info.model == CHIP_ESP32S3 ? "ESP32-S3" :
           chip_info.model == CHIP_ESP32C3 ? "ESP32-C3" : "Unknown",
           chip_info.revision);

    // 固件版本
    const esp_app_desc_t* app_desc = esp_ota_get_app_description();
    printf("Firmware: %s\n", app_desc->version);
    printf("Build Date: %s %s\n", app_desc->date, app_desc->time);

    printf("==================\n\n");

    return 0;
}

int NetworkConsole::CmdHelp(int argc, char** argv) {
    printf("\n=== Network Console Commands ===\n");

    // 检查设备是否支持双网络
    auto& board = Board::GetInstance();
    std::string board_type = board.GetBoardType();
    bool is_dual_network = (board_type == "wifi" || board_type == "ml307");

    if (is_dual_network) {
        printf("4g        - Switch to 4G network (ML307)\n");
        printf("wifi      - Switch to WiFi network\n");
        printf("status    - Show current network status\n");
        printf("nethelp   - Show this help message\n");
        printf("reboot    - Reboot the device\n");

        printf("\nDevice Info:\n");
        printf("  Type: Dual Network Board\n");
        printf("  Networks: WiFi + 4G (ML307)\n");
        printf("  Switching: ✅ Available\n");

        printf("\nUsage examples:\n");
        printf("  xiaozhi> 4g       # Switch to 4G\n");
        printf("  xiaozhi> wifi     # Switch to WiFi\n");
        printf("  xiaozhi> status   # Check network status\n");
    } else {
        printf("4g        - ❌ Not available (WiFi-only device)\n");
        printf("wifi      - Switch to WiFi network\n");
        printf("status    - Show current network status\n");
        printf("nethelp   - Show this help message\n");
        printf("reboot    - Reboot the device\n");

        printf("\nDevice Info:\n");
        printf("  Type: WiFi Only Board\n");
        printf("  Networks: WiFi only\n");
        printf("  Switching: ❌ Not available\n");

        printf("\nUsage examples:\n");
        printf("  xiaozhi> wifi     # Switch to WiFi\n");
        printf("  xiaozhi> status   # Check network status\n");
    }

    printf("\n=== Audio Broadcast Commands ===\n");
    printf("broadcast <text>  - Broadcast custom text via TTS\n");
    printf("weather          - Broadcast weather query\n");
    printf("time             - Broadcast current time\n");
    printf("play <audio_id>  - Play preloaded audio\n");
    printf("audiohelp        - Show audio commands help\n");

    printf("\n=== New Audio Test Commands ===\n");
    printf("smart_weather    - Smart weather query (AI model)\n");
    printf("smart_time       - Smart time query (AI model)\n");
    printf("direct_tts <text>- Direct TTS without AI model\n");
    printf("local <number>   - Play local audio file (001, 002, etc.)\n");

    printf("\nAudio examples:\n");
    printf("  xiaozhi> broadcast 你好世界\n");
    printf("  xiaozhi> smart_weather\n");
    printf("  xiaozhi> direct_tts 你好，很高兴认识你\n");
    printf("  xiaozhi> local 001\n");
    printf("================================\n\n");

    return 0;
}

int NetworkConsole::CmdReboot(int argc, char** argv) {
    printf("Rebooting device in 3 seconds...\n");
    for (int i = 3; i > 0; i--) {
        printf("%d...\n", i);
        vTaskDelay(pdMS_TO_TICKS(1000));
    }
    printf("Rebooting now!\n");
    esp_restart();
    return 0;
}

int NetworkConsole::CmdSwitchToWiFi(int argc, char** argv) {
    printf("Switching to WiFi network...\n");

    // 检查设备是否支持双网络
    auto& board = Board::GetInstance();
    std::string board_type = board.GetBoardType();
    bool is_dual_network = (board_type == "wifi" || board_type == "ml307");

    if (!is_dual_network) {
        printf("✅ This device only supports WiFi network.\n");
        return 0;
    }

    auto* dual_board = static_cast<DualNetworkBoard*>(&board);

    if (dual_board->GetNetworkType() == NetworkType::WIFI) {
        printf("✅ Already using WiFi network.\n");
        return 0;
    }

    printf("Saving network type to settings...\n");
    Settings settings("network", true);
    settings.SetInt("type", 0); // 0 = WiFi

    printf("Network type set to WiFi. Rebooting...\n");
    vTaskDelay(pdMS_TO_TICKS(1000));
    esp_restart();

    return 0;
}

// 音频播报测试命令实现
int NetworkConsole::CmdBroadcastText(int argc, char** argv) {
    if (argc < 2) {
        printf("Usage: broadcast <text>\n");
        printf("Example: broadcast 你好，这是测试播报\n");
        return 1;
    }

    // 拼接所有参数作为播报文本
    std::string text;
    for (int i = 1; i < argc; i++) {
        if (i > 1) text += " ";
        text += argv[i];
    }

    printf("Broadcasting text: %s\n", text.c_str());

    auto& app = Application::GetInstance();
    app.BroadcastText(text);

    return 0;
}

int NetworkConsole::CmdBroadcastWeather(int argc, char** argv) {
    printf("Broadcasting weather query...\n");

    auto& app = Application::GetInstance();
    app.BroadcastWeather();

    return 0;
}

int NetworkConsole::CmdBroadcastTime(int argc, char** argv) {
    printf("Broadcasting current time...\n");

    auto& app = Application::GetInstance();
    app.BroadcastTime();

    return 0;
}

int NetworkConsole::CmdPlayAudio(int argc, char** argv) {
    if (argc < 2) {
        printf("Usage: play <audio_id>\n");
        printf("Available audio IDs: startup, connected, error\n");
        return 1;
    }

    std::string audio_id = argv[1];
    printf("Playing audio: %s\n", audio_id.c_str());

    auto& app = Application::GetInstance();
    app.PlaySound(audio_id);

    return 0;
}

int NetworkConsole::CmdAudioHelp(int argc, char** argv) {
    printf("\n=== Audio Broadcast Commands ===\n");
    printf("broadcast <text>  - Broadcast custom text via TTS\n");
    printf("weather          - Broadcast weather query\n");
    printf("time             - Broadcast current time\n");
    printf("play <audio_id>  - Play preloaded audio (startup/connected/error)\n");
    printf("audiohelp        - Show this help message\n");

    printf("\n=== Smart Broadcast (AI Model) ===\n");
    printf("smart_weather    - Query weather using AI model\n");
    printf("smart_time       - Query time using AI model\n");

    printf("\n=== Direct TTS (No AI Model) ===\n");
    printf("direct_tts <text>- Convert text to speech directly\n");

    printf("\n=== Local Audio Files ===\n");
    printf("local <number>   - Play local audio file (001, 002, etc.)\n");

    printf("\nExamples:\n");
    printf("  xiaozhi> smart_weather           # AI: '现在天气怎么样'\n");
    printf("  xiaozhi> smart_time              # AI: '现在时间'\n");
    printf("  xiaozhi> direct_tts 你好，很高兴认识你  # Direct TTS\n");
    printf("  xiaozhi> local 001               # Play 001.mp3\n");
    printf("================================\n");

    return 0;
}

// 新的音频播报测试命令实现
int NetworkConsole::CmdSmartWeather(int argc, char** argv) {
    printf("Sending smart weather query (requires AI model)...\n");

    auto& broadcast_manager = BroadcastManager::GetInstance();
    broadcast_manager.PlaySmartInteraction("现在天气怎么样", BroadcastManager::WEATHER_REPORT, 8);

    return 0;
}

int NetworkConsole::CmdSmartTime(int argc, char** argv) {
    printf("Sending smart time query (requires AI model)...\n");

    auto& broadcast_manager = BroadcastManager::GetInstance();
    broadcast_manager.PlaySmartInteraction("现在时间", BroadcastManager::HOURLY_TIME, 8);

    return 0;
}

int NetworkConsole::CmdDirectTTS(int argc, char** argv) {
    if (argc < 2) {
        printf("Usage: direct_tts <text>\n");
        printf("Example: direct_tts 你好，很高兴认识你\n");
        return 1;
    }

    // 拼接所有参数作为TTS文本
    std::string text;
    for (int i = 1; i < argc; i++) {
        if (i > 1) text += " ";
        text += argv[i];
    }

    printf("Playing direct TTS (no AI model): %s\n", text.c_str());

    auto& broadcast_manager = BroadcastManager::GetInstance();
    broadcast_manager.PlayDirectTTS(text, BroadcastManager::CUSTOM_TEXT, 6);

    return 0;
}

int NetworkConsole::CmdPlayLocal(int argc, char** argv) {
    if (argc < 2) {
        printf("Usage: local <audio_number>\n");
        printf("Example: local 001\n");
        printf("Available audio files:\n");

        auto& local_audio = LocalAudioManager::GetInstance();
        auto available = local_audio.GetAvailableAudios();
        if (available.empty()) {
            printf("  No audio files found in /spiffs/audio/\n");
            printf("  Please add audio files like 001.mp3, 002.wav, etc.\n");
        } else {
            for (const auto& audio : available) {
                printf("  %s\n", audio.c_str());
            }
        }
        return 1;
    }

    std::string audio_number = argv[1];
    printf("Playing local audio: %s\n", audio_number.c_str());

    auto& broadcast_manager = BroadcastManager::GetInstance();
    broadcast_manager.PlayPreloadedAudio(audio_number, BroadcastManager::SYSTEM_ALERT, 9);

    return 0;
}
