#include "display.h"
#include <esp_log.h>

#define TAG "NoDisplay"

// NoDisplay implementation that doesn't depend on LVGL
class NoDisplayImpl : public Display {
public:
    NoDisplayImpl() {
        ESP_LOGI(TAG, "NoDisplay initialized - all display operations will be no-ops");
        width_ = 0;
        height_ = 0;
    }

    virtual ~NoDisplayImpl() {
        // No cleanup needed
    }

    virtual void SetStatus(const char* status) override {
        // No-op
        ESP_LOGD(TAG, "SetStatus: %s", status ? status : "null");
    }

    virtual void ShowNotification(const char* notification, int duration_ms = 3000) override {
        // No-op
        ESP_LOGD(TAG, "ShowNotification: %s (duration: %d ms)", notification ? notification : "null", duration_ms);
    }

    virtual void ShowNotification(const std::string &notification, int duration_ms = 3000) override {
        ShowNotification(notification.c_str(), duration_ms);
    }

    virtual void SetEmotion(const char* emotion) override {
        // No-op
        ESP_LOGD(TAG, "SetEmotion: %s", emotion ? emotion : "null");
    }

    virtual void SetChatMessage(const char* role, const char* content) override {
        // No-op
        ESP_LOGD(TAG, "SetChatMessage: %s: %s", role ? role : "null", content ? content : "null");
    }

    virtual void SetIcon(const char* icon) override {
        // No-op
        ESP_LOGD(TAG, "SetIcon: %s", icon ? icon : "null");
    }

    virtual void SetPreviewImage(const void* image) override {
        // No-op in no-display mode
        ESP_LOGD(TAG, "SetPreviewImage: %p (no-display mode)", image);
    }

    virtual void SetTheme(const std::string& theme_name) override {
        // No-op
        ESP_LOGD(TAG, "SetTheme: %s", theme_name.c_str());
        current_theme_name_ = theme_name;
    }

    virtual void UpdateStatusBar(bool update_all = false) override {
        // No-op
        ESP_LOGD(TAG, "UpdateStatusBar: update_all=%s", update_all ? "true" : "false");
    }

private:
    virtual bool Lock(int timeout_ms = 0) override {
        return true;  // Always succeed
    }

    virtual void Unlock() override {
        // No-op
    }
};

// Factory function to create NoDisplay instance
Display* CreateNoDisplay() {
    return new NoDisplayImpl();
}
